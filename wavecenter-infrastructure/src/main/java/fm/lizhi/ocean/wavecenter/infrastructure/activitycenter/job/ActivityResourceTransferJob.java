package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityResourceTransferManager;
import lombok.extern.slf4j.Slf4j;


/**
 * 资源转存定时任务
 * 将创作者 CDN 的资源转存到对应业务 CDN 上
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActivityResourceTransferJob implements JobHandler {

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityResourceTransferManager activityResourceTransferManager;


    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        try {
            // 处理手动执行
            executeByManual(jobExecuteContext);

            // 转存活动房间公告
            List<ActivityInfoDTO> activityInfoList = activityApplyManager.getActivityInfoByStartTimeGte(new Date());
            if (CollectionUtils.isEmpty(activityInfoList)) {
                return;
            }

            //转存活动相关图片
            transferActivityImage(activityInfoList);
            //转存流量资源图片
            transferFlowResourceImage(activityInfoList);
            //转存角标资源
            transferRoomMark(activityInfoList);

        } catch (Exception e) {
            log.error("ActivityResourceTransferJob execute error", e);
        }
    }

    /**
     * 转存角标资源
     */
    private void transferRoomMark(List<ActivityInfoDTO> activityInfoList) {
        activityInfoList.stream().filter(info -> StringUtils.isNotBlank(info.getRoomMarkUrl()))
                .forEach(info -> activityResourceTransferManager.transfer(new ActivityResourceTransferDTO()
                        .setAppId(info.getAppId())
                        .setSourceUrls(Lists.newArrayList(info.getRoomMarkUrl())))
                );
    }

    /**
     * 转存房间公告图片和海报图片
     */
    private void transferActivityImage(List<ActivityInfoDTO> activityInfoList) {
        //遍历活动列表
        for (ActivityInfoDTO info : activityInfoList) {
            List<String> urlList = new ArrayList<>();
            // 转存资源
            if (StringUtils.isNotBlank(info.getRoomAnnouncementImgUrl())) {
                List<String> announcementImgUrls = StrUtil.split(info.getRoomAnnouncementImgUrl(), CharPool.COMMA);
                urlList.addAll(announcementImgUrls);
            }
            if (StringUtils.isNotBlank(info.getPosterUrl())) {
                urlList.add(info.getPosterUrl());
            }

            //转存
            activityResourceTransferManager.transfer(new ActivityResourceTransferDTO().setAppId(info.getAppId()).setSourceUrls(urlList));
        }
    }

    /**
     * 转存流量资源
     *
     * @param activityInfoList 活动列表
     */
    private void transferFlowResourceImage(List<ActivityInfoDTO> activityInfoList) {
        List<ActivityFlowResourceDTO> flowResourceList = activityApplyManager.getActivityFlowResourceByActivityIds(activityInfoList.stream().map(ActivityInfoDTO::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(flowResourceList)) {
            return;
        }

        Map<Long, Integer> activityAppIdMap = activityInfoList.stream().collect(Collectors.toMap(ActivityInfoDTO::getId, ActivityInfoDTO::getAppId));

        for (ActivityFlowResourceDTO flowResource : flowResourceList) {
            // 转存资源
            if (StrUtil.isBlank(flowResource.getImageUrl())) {
                continue;
            }
            Integer appId = activityAppIdMap.get(flowResource.getActivityId());
            activityResourceTransferManager.transfer(new ActivityResourceTransferDTO().setAppId(appId).setSourceUrls(Lists.newArrayList(flowResource.getImageUrl())));
        }
    }

    private void executeByManual(JobExecuteContext jobExecuteContext) {

        try {
            String param = jobExecuteContext.getParam();
            log.info("execute param: {}", param);

            if (StrUtil.isBlank(param) || !JSONUtil.isTypeJSONArray(param)) {
                return;
            }

            List<ActivityResourceTransferDTO> list = JSONArray.parseArray(param, ActivityResourceTransferDTO.class);
            list.forEach(activityResourceTransferDTO -> {
                activityResourceTransferManager.transfer(activityResourceTransferDTO);
            });

        } catch (Exception e) {
            log.error("ActivityResourceTransferJob executeByManual error", e);

        }
    }
}