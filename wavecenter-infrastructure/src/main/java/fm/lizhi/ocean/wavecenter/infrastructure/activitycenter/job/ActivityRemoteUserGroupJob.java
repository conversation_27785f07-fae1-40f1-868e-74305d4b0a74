package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateInfoBean;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.config.ActivityRemoteUserGroupJobConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendReportDataInfoDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.RemoveUserGroupUserParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动结束后移除厅主白名单用户组定时任务
 * 
 * 功能：扫描已结束的活动，将活动厅主ID从白名单用户组中移除
 * 
 * 流程：
 * 1. 获取近5分钟（可配置）已结束的活动
 * 2. 根据活动查询模板中的whiteNjUserGroupId
 * 3. 检查厅主是否有即将进行或正在进行的活动，如有则跳过
 * 4. 从用户组中移除厅主
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActivityRemoteUserGroupJob implements JobHandler {

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Autowired
    private UserGroupManager userGroupManager;

    @Autowired
    private ActivityRemoteUserGroupJobConfig config;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        // 检查是否启用
        if (!config.getEnabled()) {
            log.info("ActivityRemoteUserGroupJob is disabled, skipping execution");
            return;
        }

        log.info("ActivityRemoteUserGroupJob start execute. endedActivityGapTimeMin={}, upcomingActivityGapTimeMin={}",
                config.getEndedActivityGapTimeMin(), config.getUpcomingActivityGapTimeMin());

        try {
            // 1. 获取已结束的活动列表
            List<SendReportDataInfoDTO> endedActivities = activityApplyManager.querySendReportDataInfoList(config.getEndedActivityGapTimeMin());
            
            if (CollectionUtils.isEmpty(endedActivities)) {
                log.info("ActivityRemoteUserGroupJob no ended activities found");
                return;
            }

            log.info("ActivityRemoteUserGroupJob found {} ended activities", endedActivities.size());

            // 2. 处理每个已结束的活动
            for (SendReportDataInfoDTO activity : endedActivities) {
                processEndedActivity(activity);
            }

            log.info("ActivityRemoteUserGroupJob execute completed successfully");

        } catch (Exception e) {
            log.error("ActivityRemoteUserGroupJob execute failed", e);
            throw e;
        }
    }

    /**
     * 处理单个已结束的活动
     */
    private void processEndedActivity(SendReportDataInfoDTO activity) {
        Long activityId = activity.getActivityId();
        Long njId = activity.getNjId();
        Long whiteNjUserGroupId = null;

        try {
            if (config.getVerboseLogging()) {
                log.info("ActivityRemoteUserGroupJob processing activity. activityId={}, njId={}, endTime={}",
                        activityId, njId, activity.getEndTime());
            } else {
                log.info("ActivityRemoteUserGroupJob processing activity. activityId={}, njId={}", activityId, njId);
            }

            // 参数校验
            if (activityId == null || njId == null) {
                log.warn("ActivityRemoteUserGroupJob invalid activity data. activityId={}, njId={}", activityId, njId);
                return;
            }

            // 1. 获取活动模板信息
            ActivityTemplateInfoBean templateInfo = getActivityTemplateInfo(activityId);
            if (templateInfo == null) {
                return;
            }

            whiteNjUserGroupId = templateInfo.getWhiteNjUserGroupId();
            if (whiteNjUserGroupId == null) {
                log.info("ActivityRemoteUserGroupJob no whiteNjUserGroupId found. activityId={}, templateId={}",
                        activityId, templateInfo.getId());
                return;
            }

            // 2. 检查厅主是否有即将进行或正在进行的活动
            if (hasUpcomingActivity(njId)) {
                log.info("ActivityRemoteUserGroupJob skip due to upcoming activity. activityId={}, njId={}, userGroupId={}",
                        activityId, njId, whiteNjUserGroupId);
                return;
            }

            // 3. 从用户组中移除厅主
            removeUserFromGroup(njId, whiteNjUserGroupId);

            log.info("ActivityRemoteUserGroupJob success. activityId={}, njId={}, userGroupId={}",
                    activityId, njId, whiteNjUserGroupId);

        } catch (Exception e) {
            log.error("ActivityRemoteUserGroupJob failed to process activity. activityId={}, njId={}, userGroupId={}",
                    activityId, njId, whiteNjUserGroupId, e);
            // 单个活动处理失败不影响其他活动的处理
        }
    }

    /**
     * 获取活动模板信息
     */
    private ActivityTemplateInfoBean getActivityTemplateInfo(Long activityId) {
        try {
            ActivityTemplateInfoBean templateInfo = activityTemplateManager.getTemplateInfoBeanByActivityId(activityId);
            if (templateInfo == null) {
                log.warn("ActivityRemoteUserGroupJob template not found. activityId={}", activityId);
                return null;
            }

            if (config.getVerboseLogging()) {
                log.debug("ActivityRemoteUserGroupJob found template. activityId={}, templateId={}, whiteNjUserGroupId={}",
                        activityId, templateInfo.getId(), templateInfo.getWhiteNjUserGroupId());
            }

            return templateInfo;

        } catch (Exception e) {
            log.error("ActivityRemoteUserGroupJob failed to get template info. activityId={}", activityId, e);
            return null;
        }
    }

    /**
     * 检查厅主是否有即将进行或正在进行的活动
     */
    private boolean hasUpcomingActivity(Long njId) {
        try {
            List<ActivityInfoDTO> upcomingActivities = activityApplyManager.queryUpcomingActivitiesByNjId(njId, config.getUpcomingActivityGapTimeMin());
            boolean hasUpcoming = CollectionUtils.isNotEmpty(upcomingActivities);

            if (hasUpcoming) {
                log.info("ActivityRemoteUserGroupJob found {} upcoming activities for njId={}",
                        upcomingActivities.size(), njId);
            } else {
                log.debug("ActivityRemoteUserGroupJob no upcoming activities found for njId={}", njId);
            }

            return hasUpcoming;

        } catch (Exception e) {
            log.error("ActivityRemoteUserGroupJob failed to check upcoming activities. njId={}", njId, e);
            // 查询失败时保守处理，跳过移除操作
            return true;
        }
    }

    /**
     * 从用户组中移除用户
     */
    private void removeUserFromGroup(Long userId, Long groupId) {
        try {
            RemoveUserGroupUserParamDTO param = new RemoveUserGroupUserParamDTO();
            param.setUserId(userId);
            param.setGroupId(groupId);

            Result<Void> result = userGroupManager.removeUserGroupUser(param);
            if (result == null || !result.isSuccess()) {
                log.error("ActivityRemoteUserGroupJob failed to remove user from group. userId={}, groupId={}, result={}", 
                        userId, groupId, result);
                return;
            }

            log.info("ActivityRemoteUserGroupJob successfully removed user from group. userId={}, groupId={}", 
                    userId, groupId);

        } catch (Exception e) {
            log.error("ActivityRemoteUserGroupJob failed to remove user from group. userId={}, groupId={}", 
                    userId, groupId, e);
            throw e;
        }
    }
}
