package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.List;

/**
 * 装扮
 * <AUTHOR>
 */
public interface IDecorateRemote extends IRemote {


    /**
     * 获取装扮列表
     */
    @Deprecated
    Result<PageBean<DecorateDTO>> getDecorateList(RequestGetDecorate request);


    /**
     * 批量获取装扮列表
     */
    @Deprecated
    Result<List<DecorateDTO>> batchGetDecorateList(RequestBatchGetDecorate request);

    int BATCH_GET_DECORATE_LIST_FAIL = 1;
    int BATCH_GET_DECORATE_LIST_ILLEGAL_PARAM = 2;

    int GET_DECORATE_LIST_ILLEGAL_PARAM = 1;
}
