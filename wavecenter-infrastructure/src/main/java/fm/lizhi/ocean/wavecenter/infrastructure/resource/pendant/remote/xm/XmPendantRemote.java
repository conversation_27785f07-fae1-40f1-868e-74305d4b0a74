package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.xm;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.convert.PendantConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import fm.lizhi.xm.content.api.PendantService;
import fm.lizhi.xm.content.model.req.pendant.QueryPendantByIdsReq;
import fm.lizhi.xm.content.model.resp.pendant.QueryPendantByIdsResp;
import fm.lizhi.xm.content.protocol.PendantServiceProto;
import fm.lizhi.xm.content.protocol.PendantServiceProto.PendantProbuf;
import fm.lizhi.xm.content.services.PendantSpringService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XmPendantRemote implements PendantRemote {

    @Autowired
    private PendantService pendantService;

    @Autowired
    private PendantSpringService pendantSpringService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public List<PendantDto> getByIds(List<Long> ids, int appId) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        try {
            return ids.stream().map(id -> {

                Result<PendantServiceProto.ResponseGetPendant> result = pendantService.getPendant(id);
                if (RpcResult.isFail(result)) {
                    log.warn("xm getPendant fail. rCode={},id={}", result.rCode(), id);
                    return null;
                }
                return PendantConvert.I.toPendantDto(result.target().getPendant());
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("XmPendantRemote getByIds error, ids: {}, appId: {}", ids, appId, e);
            return Collections.emptyList();
        }
    }


    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        PendantProbuf pendantProbuf = PendantProbuf.newBuilder()
                .setId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .build();
        Result<Void> result = pendantService.updatePendant(pendantProbuf);
        if (RpcResult.isFail(result)) {
            log.warn("xm updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return RpcResult.fail(UPDATE_PENDANT_SHOW_TIME_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        Result<Void> result = pendantService.updatePendantStatus(param.getPendantId(), param.getStatus().getStatus());
        if (RpcResult.isFail(result)) {
            log.warn("xm updatePendantStatus fail. rCode={},pendantId={},status={}", result.rCode(), param.getPendantId(), param.getStatus());
            return RpcResult.fail(UPDATE_PENDANT_STATUS_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<List<PendantDto>> getPendantWithConditions(List<Long> pendantIds) {
        QueryPendantByIdsReq req = new QueryPendantByIdsReq();
        req.setPendantId(pendantIds.get(0));
        Result<QueryPendantByIdsResp> result = pendantSpringService.queryPendantByIds(req);
        if (RpcResult.isFail(result)) {
            log.warn("xm getPendantWithConditions fail. rCode={},pendantIds={}", result.rCode(), pendantIds);
            return RpcResult.fail(GET_PENDANT_WITH_CONDITIONS_FAIL);
        }
        return RpcResult.success(Lists.newArrayList(PendantConvert.I.toPendantConditionDtoXm(result.target().getPendantInfo())));
    }

}
