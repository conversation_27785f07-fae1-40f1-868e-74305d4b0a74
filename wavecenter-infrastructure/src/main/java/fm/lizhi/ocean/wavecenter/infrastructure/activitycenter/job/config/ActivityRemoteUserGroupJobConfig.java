package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ActivityRemoteUserGroupJob 定时任务配置
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "activity.remote.usergroup.job")
public class ActivityRemoteUserGroupJobConfig {

    /**
     * 已结束活动查询时间间隔（分钟）
     * 默认值：5分钟
     */
    private Integer endedActivityGapTimeMin = 5;

    /**
     * 即将进行活动查询时间间隔（分钟）
     * 默认值：20分钟
     */
    private Integer upcomingActivityGapTimeMin = 20;

    /**
     * 是否启用定时任务
     * 默认值：true
     */
    private Boolean enabled = true;

    /**
     * 批量处理大小
     * 默认值：100
     */
    private Integer batchSize = 100;

    /**
     * 是否启用详细日志
     * 默认值：false
     */
    private Boolean verboseLogging = false;
}
