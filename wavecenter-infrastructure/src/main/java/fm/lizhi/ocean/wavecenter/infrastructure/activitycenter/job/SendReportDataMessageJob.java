package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.chat.manager.ChatManagerImpl;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendReportDataInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityReportDataManager;
import lombok.extern.slf4j.Slf4j;


/**
 * 发送数据报告通知
 * <AUTHOR>
 */
@Slf4j
@Component
public class SendReportDataMessageJob implements JobHandler {

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ChatManagerImpl chatManager;

    @Autowired
    private ActivityReportDataManager activityReportDataManager;


    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {

        log.info("send report data message job start.");

        int recentMin = activityConfig.getActivityNotifyGapTimeMin();
        long sendCount = 0;
        //开始
        List<SendReportDataInfoDTO> list = activityApplyManager.querySendReportDataInfoList(recentMin);
        log.info("first query size={}", list.size());
        while (CollUtil.isNotEmpty(list) && sendCount < 500){
            list = filterNotExistActivity(list);
            log.info("filter list size={}", list.size());

            if (CollUtil.isNotEmpty(list)){
                sendCount += sendReportDataMessage(list);
            } else {
                break;
            }

            list = activityApplyManager.querySendReportDataInfoList(recentMin);
            log.info("while query size={}", list.size());
        }

        log.info("send report data message job end. count:{}", sendCount);
    }


    /**
     * 过滤报告未出或不存在的活动
     */
    public List<SendReportDataInfoDTO>  filterNotExistActivity(List<SendReportDataInfoDTO> list){

        List<ActivityReportDataSummaryBean> summaryList = activityReportDataManager.batchGetReportSummary(
                list.stream().map(SendReportDataInfoDTO::getActivityId).collect(Collectors.toList())
        );

        List<Long> existActivityIds = summaryList.stream().map(ActivityReportDataSummaryBean::getActivityId).collect(Collectors.toList());
        list.removeIf(info -> !existActivityIds.contains(info.getActivityId()));
        return list;
    }

    public long sendReportDataMessage(List<SendReportDataInfoDTO> list){
        for (SendReportDataInfoDTO info : list) {
            //处理跨天
            String message = String.format("你的活动数据日报(%s)已生成，请前往“创作中心”查看",
                    DateUtil.formatDateToString(info.getEndTime(), DateUtil.yyyy_MM_dd));
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(info.getAppId()));
            chatManager.sendChatAsync(info.getNjId(), message);
            chatManager.sendChatAsync(info.getApplicantUid(), message);
        }
        List<Long> activityIds = list.stream().map(SendReportDataInfoDTO::getActivityId).collect(Collectors.toList());
        int updateCount = activityApplyManager.batchUpdateSendReportStatus(activityIds);
        log.info("batchUpdateSendReportStatus finish;updateCount={};ids={}", updateCount, activityIds);
        return list.size();
    }
}