package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.convert;

import fm.lizhi.hy.content.protocol.PendantServiceProto.PendantConditionProbuf;
import fm.lizhi.hy.content.protocol.PendantServiceProto.AllStatusPendantProbuf;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantConditionDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.pp.content.assistant.datas.ConditionInfo;
import fm.lizhi.pp.content.assistant.datas.PendantInfo;
import fm.lizhi.pp.content.assistant.protocol.PendantServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface PendantConvert {

    PendantConvert I = Mappers.getMapper(PendantConvert.class);

    List<PendantDto> toPendantDtoListPp(List<PendantServiceProto.PendantProbuf> target);
    
    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDtoPp(PendantServiceProto.PendantProbuf target);

    List<PendantDto> toPendantDtoListHy(List<fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf> pendantsList);

    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDtoHy(fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf pendant);

    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDto(fm.lizhi.xm.content.protocol.PendantServiceProto.PendantProbuf pendant);


    @Mapping(target = "conditions", source =  "conditionInfoList")
    @Mapping(target = "imageUrl", source = "image")
    PendantDto toPendantConditionDtoPp(PendantInfo pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")    
    PendantConditionDTO toPendantConditionDtoPp(ConditionInfo pendantCondition);

    @Mapping(target = "conditions", source =  "conditionInfoList")
    @Mapping(target = "imageUrl", source = "image")
    PendantDto toPendantConditionDtoXm(fm.lizhi.xm.content.bean.pendant.PendantInfo pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")    
    PendantConditionDTO toPendantConditionDtoXm(fm.lizhi.xm.content.bean.ConditionInfo pendantCondition);

    @Mapping(target = "conditions", source =  "conditionsList")
    PendantDto toPendantConditionDtoHy(AllStatusPendantProbuf pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")    
    PendantConditionDTO toPendantConditionDtoHy(PendantConditionProbuf pendantCondition);
}
