package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserGroupRemote;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.AddUserGroupUserParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.CreateUserGroupParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.CreateUserGroupResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.RemoveUserGroupUserParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:43
 */
@Component
public class UserGroupManagerImpl implements UserGroupManager {

    @Autowired
    private IUserGroupRemote iUserGroupRemote;

    @Override
    public boolean isUserInGroup(Long userId, Long groupId) {
        return iUserGroupRemote.isUserInGroup(userId, groupId);
    }

    @Override
    public Result<CreateUserGroupResultDTO> createUserGroup(CreateUserGroupParamDTO param) {
        return iUserGroupRemote.createUserGroup(param);
    }

    @Override
    public Result<Void> addUserGroupUser(AddUserGroupUserParamDTO param) {
        return iUserGroupRemote.addUserGroupUser(param);
    }

    @Override
    public Result<Void> removeUserGroupUser(RemoveUserGroupUserParamDTO param) {
        return iUserGroupRemote.removeUserGroupUser(param);
    }
}
