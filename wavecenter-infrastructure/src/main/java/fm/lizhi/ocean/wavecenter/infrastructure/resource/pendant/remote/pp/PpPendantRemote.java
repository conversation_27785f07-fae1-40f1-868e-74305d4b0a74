package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.pp;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.convert.PendantConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import fm.lizhi.pp.content.assistant.api.PendantService;
import fm.lizhi.pp.content.assistant.api.PendantSpringService;
import fm.lizhi.pp.content.assistant.dto.req.ReqQueryPendantsByIds;
import fm.lizhi.pp.content.assistant.dto.res.ResQueryPendantsByIds;
import fm.lizhi.pp.content.assistant.protocol.PendantServiceProto;
import fm.lizhi.pp.content.assistant.protocol.PendantServiceProto.PendantProbuf;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class PpPendantRemote implements PendantRemote {


    @Autowired
    private PendantService ppPendantService;

    @Autowired
    private PendantSpringService ppPendantSpringService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<PendantDto> getByIds(List<Long> ids, int appId) {

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        try {
            Result<PendantServiceProto.ResponseGetPendants> result = ppPendantService.getPendants(ids);
            if (RpcResult.isFail(result)) {
                log.warn("pp getPendants fail. rCode={},ids={}", result.rCode(), ids);
                return Collections.emptyList();
            }

            return PendantConvert.I.toPendantDtoListPp(result.target().getPendantsList());

        } catch (Exception e) {
            log.error("PpPendantRemote getByIds error, ids: {}, appId: {}", ids, appId, e);
            return Collections.emptyList();
        }
    }


    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        // 获取挂件信息，全量设置回去

        PendantProbuf pendantProbuf = PendantProbuf.newBuilder()
                .setId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .build();
        Result<Void> result = ppPendantService.updatePendant(pendantProbuf);
        if (RpcResult.isFail(result)) {
            log.warn("pp updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return RpcResult.fail(UPDATE_PENDANT_SHOW_TIME_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        
        Result<Void> result = ppPendantService.updatePendantStatus(param.getPendantId(), param.getStatus().getStatus());
        if (RpcResult.isFail(result)) {
            log.warn("pp updatePendantStatus fail. rCode={},pendantId={},status={}", result.rCode(), param.getPendantId(), param.getStatus());
            return RpcResult.fail(UPDATE_PENDANT_STATUS_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<List<PendantDto>> getPendantWithConditions(List<Long> pendantIds) {
        ReqQueryPendantsByIds reqQueryPendantsByIds = new ReqQueryPendantsByIds();
        reqQueryPendantsByIds.setPendantId(pendantIds.get(0));
        Result<ResQueryPendantsByIds> result = ppPendantSpringService.queryPendantsByIds(reqQueryPendantsByIds);
        if (RpcResult.isFail(result)) {
            log.warn("pp getPendantWithConditions fail. rCode={},pendantIds={}", result.rCode(), pendantIds);
            return RpcResult.fail(GET_PENDANT_WITH_CONDITIONS_FAIL);
        }
        return RpcResult.success(Lists.newArrayList(PendantConvert.I.toPendantConditionDtoPp(result.target().getPendantInfo())));
    }

}
