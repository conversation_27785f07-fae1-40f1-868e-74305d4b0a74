package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.net.UrlEscapers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfo;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Component
public class ActivityChatManagerImpl implements ActivityChatManager {

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ChatManager chatManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ActivityApplyDao activityApplyDao;

    /**
     * 线程池
     */
    private static ExecutorService executorService = ThreadUtils.getTtlExecutors("activityChatManager", 8, 16);

    @Override
    public void userCancelActivityNotice(ActivityInfoDTO activityInfo, Long operateUserId) {
        executorService.execute(() -> {
            // 构建通知用户ID列表
            try {
                List<Long> userIds = buildNotifyUserIds(activityInfo);
                String startDate = DateUtil.formatDateToString(activityInfo.getStartTime(), "yyyy-MM-dd HH:mm");
                String endTime = DateUtil.formatDateToString(activityInfo.getEndTime(), "HH:mm");

                Map<Long, SimpleUserDto> simpleUserMap = userManager.getSimpleUserMapByIds(Lists.newArrayList(activityInfo.getNjId(), operateUserId));

                // 查询操作信息
                SimpleUserDto operateUserInfo = simpleUserMap.get(operateUserId);
                if (operateUserInfo == null) {
                    log.warn("userCancelActivityNotice.simpleUserDto is null.activityId: {}, userId: {}", activityInfo.getId(), operateUserId);
                    return;
                }

                // 查询主播信息
                SimpleUserDto njUserInfo = simpleUserMap.get(activityInfo.getNjId());
                if (njUserInfo == null) {
                    // 日志增加活动ID
                    log.warn("userCancelActivityNotice.simpleUserDto is null.activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getNjId());
                    return;
                }

                // 构建取消活动文案
                String cancelActivityText = String.format(activityConfig.getCancelActivityText(),
                        njUserInfo.getName(), njUserInfo.getBand(), startDate, endTime, activityInfo.getName(), operateUserInfo.getName(),
                        operateUserInfo.getBand());
                
                // 批量发送富文本私信
                String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
                Map<String, Object> urlAction = buildActivityStartUrlAction(activityInfo.getId(), activityInfo.getStartTime(), activityInfo.getEndTime());
                batchSendRichTextChatAsync(userIds, cancelActivityText, url, urlAction);
                
            } catch (Exception e) {
                log.error("userCancelActivityNotice error, activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getApplicantUid(), e);
            }
        });
    }


    @Override
    public void adminCancelActivityNotice(ActivityInfoDTO activityInfo) {
        executorService.execute(() -> {
            // 构建通知用户ID列表
            try {
                List<Long> userIds = buildNotifyUserIds(activityInfo);
                String startDate = DateUtil.formatDateToString(activityInfo.getStartTime(), "yyyy-MM-dd HH:mm");
                String endTime = DateUtil.formatDateToString(activityInfo.getEndTime(), "HH:mm");

                Map<Long, SimpleUserDto> simpleUserMap = userManager.getSimpleUserMapByIds(Lists.newArrayList(activityInfo.getNjId()));

                // 查询主播信息
                SimpleUserDto njUserInfo = simpleUserMap.get(activityInfo.getNjId());
                if (njUserInfo == null) {
                    // 日志增加活动ID
                    log.warn("adminCancelActivityNotice.simpleUserDto is null.activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getNjId());
                    return;
                }

                // 构建取消活动文案
                String cancelActivityText = String.format(activityConfig.getAdminCancelActivityText(),
                        njUserInfo.getName(), njUserInfo.getBand(), startDate, endTime, activityInfo.getName());

                // 批量发送富文本私信
                String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
                Map<String, Object> urlAction = buildActivityStartUrlAction(activityInfo.getId(), activityInfo.getStartTime(), activityInfo.getEndTime());
                batchSendRichTextChatAsync(userIds, cancelActivityText, url, urlAction);
            } catch (Exception e) {
                log.error("adminCancelActivityNotice error, activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getApplicantUid(), e);
            }
        });
    }

    @Override
    public void agreeActivityNotice(ActivityInfoDTO activityInfo) {
        executorService.execute(() -> {
            try {
                // 构建通知用户ID列表
                List<Long> userIds = buildNotifyUserIds(activityInfo);
                // 获取主播信息
                Optional<UserInfoDto> njUserInfo = userManager.getUserInfoById(activityInfo.getNjId());
                // 构建同意活动文案
                String applyAgreeText = activityConfig.getApplyAgreeText();
                String startDate = DateUtil.formatDateToString(activityInfo.getStartTime(), "yyyy-MM-dd HH:mm");
                String endTime = DateUtil.formatDateToString(activityInfo.getEndTime(), "HH:mm");
                applyAgreeText = String.format(applyAgreeText, njUserInfo.map(UserInfoDto::getName).orElse(activityInfo.getNjId().toString()), 
                            njUserInfo.map(UserInfoDto::getBand).orElse(activityInfo.getNjId().toString()), startDate, endTime, activityInfo.getName());

                // 批量发送富文本私信
                String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
                Map<String, Object> urlAction = buildActivityStartUrlAction(activityInfo.getId(), activityInfo.getStartTime(), activityInfo.getEndTime());
                batchSendRichTextChatAsync(userIds, applyAgreeText, url, urlAction);
            } catch (Exception e) {
                log.error("agreeActivityNotice error, activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getApplicantUid(), e);
            }
        });

    }

    @Override
    public void rejectedActivityNotice(ActivityInfoDTO activityInfo) {
        executorService.execute(() -> {
            try {
                // 构建通知用户ID列表
                List<Long> userIds = buildNotifyUserIds(activityInfo);

                // 获取主播信息
                Optional<UserInfoDto> njUserInfo = userManager.getUserInfoById(activityInfo.getNjId());
                String applyRejectText = activityConfig.getApplyRejectText();
                String njName = njUserInfo.map(UserInfoDto::getName).orElse(activityInfo.getNjId().toString());
                String njBand = njUserInfo.map(UserInfoDto::getBand).orElse(activityInfo.getNjId().toString());
                String startDate = DateUtil.formatDateToString(activityInfo.getStartTime(), "yyyy-MM-dd HH:mm");
                String endTime = DateUtil.formatDateToString(activityInfo.getEndTime(), "HH:mm");
                String activityName = activityInfo.getName();
                // 构建拒绝文案
                applyRejectText = String.format(applyRejectText, njName, njBand, startDate, endTime, activityName);
                // 批量发送富文本私信
                String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
                Map<String, Object> urlAction = buildActivityStartUrlAction(activityInfo.getId(), activityInfo.getStartTime(), activityInfo.getEndTime());
                batchSendRichTextChatAsync(userIds, applyRejectText, url, urlAction);
            } catch (Exception e) {
                log.error("rejectedActivityNotice error, activityId: {}, userId: {}", activityInfo.getId(), activityInfo.getApplicantUid(), e);
            }
        });
    }


    @Override
    public void startActivityNotice() {
        //查询出一小时后开始的活动
        List<ActivityApplyInfo> activityApplyList = activityApplyDao.getNextHourStartActivityList(activityConfig.getNoticeStartActivityPreactHour());
        if (CollectionUtils.isEmpty(activityApplyList)) {
            log.info("startActivityNotice.no activity apply info");
            return;
        }

        //按appId分组
        Map<Integer, List<ActivityApplyInfo>> activityApplyMap = activityApplyList.stream().collect(Collectors.groupingBy(ActivityApplyInfo::getAppId));
        //遍历activityApplyMap
        for (Map.Entry<Integer, List<ActivityApplyInfo>> entry : activityApplyMap.entrySet()) {
            ResultHandler.handle(entry.getKey(), () -> {
                startActivityNoticeCore(entry.getValue());
                return RpcResult.success();
            });
        }
    }

    @Override
    public void endActivityNotice() {
        List<ActivityApplyInfo> activityApplyList = activityApplyDao.getLastDayEndActivityList();
        if (CollectionUtils.isEmpty(activityApplyList)) {
            return;
        }

        //按appId分组
        Map<Integer, List<ActivityApplyInfo>> activityApplyMap = activityApplyList.stream().collect(Collectors.groupingBy(ActivityApplyInfo::getAppId));
        //遍历activityApplyMap
        for (Map.Entry<Integer, List<ActivityApplyInfo>> entry : activityApplyMap.entrySet()) {
            ResultHandler.handle(entry.getKey(), () -> {
                endActivityNoticeCore(entry.getValue());
                return RpcResult.success();
            });
        }
    }

    /**
     * 活动开始通知核心流程
     *
     * @param activityApplyList 活动列表
     */
    private void startActivityNoticeCore(List<ActivityApplyInfo> activityApplyList) {
        List<Long> njIds = activityApplyList.stream().map(ActivityApplyInfo::getNjId).collect(Collectors.toList());
        Map<Long, SimpleUserDto> simpleUserMap = userManager.getSimpleUserMapByIds(njIds);

        int totalActivitySize = activityApplyList.size();
        int successCount = 0;

        String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
        for (ActivityApplyInfo activityApplyInfo : activityApplyList) {
            try {
                // 构建通知用户ID列表
                List<Long> userIds = buildNotifyUserIds(activityApplyInfo);
                if (!simpleUserMap.containsKey(activityApplyInfo.getNjId())) {
                    log.warn("startActivityNotice.simpleUserDto is null.activityId: {}, userId: {}", activityApplyInfo.getId(), activityApplyInfo.getNjId());
                    continue;
                }

                SimpleUserDto njUserInfo = simpleUserMap.get(activityApplyInfo.getNjId());
                String activityStartText = buildActivityStartContext(activityApplyInfo, njUserInfo);
                // 批量发送富文本私信
                Map<String, Object> urlAction = buildActivityStartUrlAction(activityApplyInfo.getId(), activityApplyInfo.getStartTime(), activityApplyInfo.getEndTime());
                batchSendRichTextChatAsync(userIds, activityStartText, url, urlAction);
                successCount++;
            } catch (Exception e) {
                log.error("startActivityNotice error, activityId: {}, userId: {}", activityApplyInfo.getId(), activityApplyInfo.getNjId(), e);
            }
        }
        log.info("startActivityNotice successCount: {}, totalActivitySize: {}", successCount, totalActivitySize);
    }

    /**
     * 活动结束后通知核心流程
     *
     * @param activityApplyList 活动列表
     */
    private void endActivityNoticeCore(List<ActivityApplyInfo> activityApplyList) {
        List<Long> njIds = activityApplyList.stream().map(ActivityApplyInfo::getNjId).collect(Collectors.toList());
        Map<Long, SimpleUserDto> simpleUserMap = userManager.getSimpleUserMapByIds(njIds);

        int totalActivitySize = activityApplyList.size();
        int successCount = 0;
        // 构建web站链接
        String url = UrlUtils.addHostOrEmpty(activityConfig.getBizConfig().getWebActivityRecordUrl(), activityConfig.getBizConfig().getWaveCenterDomain());
        for (ActivityApplyInfo activityApplyInfo : activityApplyList) {
            // 构建通知用户ID列表
            try {
                List<Long> userIds = buildNotifyUserIds(activityApplyInfo);
                if (!simpleUserMap.containsKey(activityApplyInfo.getNjId())) {
                    log.warn("endActivityNotice.simpleUserDto is null.activityId: {}, userId: {}", activityApplyInfo.getId(), activityApplyInfo.getNjId());
                    continue;
                }

                SimpleUserDto njUserInfo = simpleUserMap.get(activityApplyInfo.getNjId());
                String activityEndText = buildActivityEndContext(activityApplyInfo, njUserInfo);
                Map<String, Object> urlAction = buildActivityEndUrlAction();
                // 批量发送富文本私信
                batchSendRichTextChatAsync(userIds, activityEndText, url, urlAction);
                successCount++;
            } catch (Exception e) {
                log.error("endActivityNotice error, activityId: {}, userId: {}", activityApplyInfo.getId(), activityApplyInfo.getNjId(), e);
            }
        }
        log.info("endActivityNotice successCount: {}, totalActivitySize: {}", successCount, totalActivitySize);
    }

    /**
     * 构建通知的用户ID列表
     *
     * @param activityInfo 活动信息
     * @return 用户ID列表
     */
    private List<Long> buildNotifyUserIds(ActivityInfoDTO activityInfo) {
        return buildNotifyUserIds(activityInfo.getApplicantUid(), activityInfo.getNjId(), activityInfo.getHostId(), activityInfo.getAccompanyNjIds());
    }

    /**
     * 构建通知的用户ID列表
     *
     * @param activityApplyInfo 活动信息
     * @return 用户ID列表
     */
    private List<Long> buildNotifyUserIds(ActivityApplyInfo activityApplyInfo) {
        return buildNotifyUserIds(activityApplyInfo.getApplicantUid(), activityApplyInfo.getNjId(), activityApplyInfo.getHostId(), activityApplyInfo.getAccompanyNjIds());
    }

    private List<Long> buildNotifyUserIds(Long applicantUid, Long njId, Long hostId, String accompanyNjIds) {
        Set<Long> userIds = new HashSet<>();
        userIds.add(applicantUid);
        userIds.add(njId);

        if (hostId != null && hostId > 0) {
            userIds.add(hostId);
        }

        if (StringUtils.isNotEmpty(accompanyNjIds)) {
            userIds.addAll(Arrays.stream(accompanyNjIds.split(","))
                    .map(String::trim) // 去除空格
                    .map(Long::parseLong) // 转换为long
                    .collect(Collectors.toList()));
        }

        return new ArrayList<>(userIds);
    }

    /**
     * 批量发送富文本私信
     *
     * @param receiverUserIdList 接收者用户ID列表
     * @param content            文案
     * @param url                链接
     */
    private void batchSendRichTextChatAsync(List<Long> receiverUserIdList, String content, String url, Map<String, Object> urlAction) {
        for (Long userId : receiverUserIdList) {
            try {
                String tempUrl = url;
                urlAction.put("targetUid", String.valueOf(userId));
                tempUrl = tempUrl + UrlEscapers.urlFragmentEscaper().escape("?action=" + JSONObject.toJSONString(urlAction));
                chatManager.sendChatAsyncWithSkipWeb(userId, content, tempUrl);
                //等待一下，不要调用的太频繁
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("sendRichTextChatAsync error. userId: {}, content: {}", userId, content, e);
            }
        }
    }

    /**
     * 构建活动开始文案
     *
     * @param activityApplyInfo 活动信息
     * @param njUserInfo        主播信息
     * @return 文案
     */
    private String buildActivityStartContext(ActivityApplyInfo activityApplyInfo, SimpleUserDto njUserInfo) {
        String startDate = DateUtil.formatDateToString(activityApplyInfo.getStartTime(), "yyyy-MM-dd HH:mm");
        String endTime = DateUtil.formatDateToString(activityApplyInfo.getEndTime(), "HH:mm");
        return String.format(activityConfig.getActivityStartNoticeText(), njUserInfo.getName(), njUserInfo.getBand(), startDate, endTime, activityApplyInfo.getName());
    }

    /**
     * 构建活动结束文案
     *
     * @param activityApplyInfo 活动信息
     * @param njUserInfo        主播信息
     * @return 文案
     */
    private String buildActivityEndContext(ActivityApplyInfo activityApplyInfo, SimpleUserDto njUserInfo) {
        String startDate = DateUtil.formatDateToString(activityApplyInfo.getStartTime(), "yyyy-MM-dd HH:mm");
        String endTime = DateUtil.formatDateToString(activityApplyInfo.getEndTime(), "HH:mm");
        return String.format(activityConfig.getActivityEndNoticeText(),njUserInfo.getName(), njUserInfo.getBand(), startDate, endTime, activityApplyInfo.getName());
    }

    /**
     * 构建活动开始url的action
     *
     * @param activityInfo 活动信息
     * @return 链接
     */
    private Map<String, Object> buildActivityStartUrlAction(Long activityId, Date startDate, Date endDate) {
        // url后面拼接action, 具体格式如下：
        //?action={"page":"/app/activity/calendar","type":1,"targetUid":"1297561159729179906","search":{"activityId":"123","startTime":111111,"endTime":11111}}
        // 根据活动时间，获取活动当天的拼活动开始当天的00:00:00 时间戳和活动结束当天的23:59:59 时间戳
        long startTime = DateUtil.getDayStart(startDate).getTime();
        long endTime = DateUtil.getDayEnd(endDate).getTime();
        //创建search
        Map<String, Object> search = new HashMap<>();
        search.put("activityId", String.valueOf(activityId));
        search.put("minStartTime", startTime);
        search.put("maxStartTime", endTime);

        //创建action
        Map<String, Object> action = new HashMap<>();
        action.put("page", activityConfig.getActivityCalendarUrl());
        action.put("type", 1);
        action.put("search", search);
        //补全url，并进行一次url编码，防止url中包含特殊字符
        return action;
    }

    /**
     * 构建活动结束url的action
     *
     * @param activityInfo 活动信息
     * @return 链接
     */
    private Map<String, Object> buildActivityEndUrlAction() {
        // url后面拼接action, 具体格式如下：
        //?action={page:'/app/activity/calendar',type:1,targetUid:'1297561159729179906',search:{startTime:111111,//拼昨天的00:00:00时间戳endTime:11111,//拼昨天的23:59:59时间戳},}
        // 根据活动时间，获取活动结束当天的00:00:00 时间戳和活动结束当天的23:59:59 时间戳
        Date yesterday = DateUtil.getDayBefore(new Date(), 1);
        long startTime = DateUtil.getDayStart(yesterday).getTime();
        long endTime = DateUtil.getDayEnd(yesterday).getTime();
        //创建search
        Map<String, Object> search = new HashMap<>();
        search.put("minStartTime", startTime);
        search.put("maxStartTime", endTime);

        //创建action
        Map<String, Object> action = new HashMap<>();
        action.put("page", activityConfig.getActivityDataCalendarUrl());
        action.put("type", 1);
        action.put("search", search);
        return action;
    }

}
