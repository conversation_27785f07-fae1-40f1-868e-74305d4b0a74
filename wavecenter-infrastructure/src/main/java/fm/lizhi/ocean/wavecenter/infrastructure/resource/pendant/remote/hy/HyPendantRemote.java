package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.content.api.PendantService;
import fm.lizhi.hy.content.protocol.PendantServiceProto;
import fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.convert.PendantConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class HyPendantRemote implements PendantRemote {


    @Autowired
    private PendantService hyPendantService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public List<PendantDto> getByIds(List<Long> ids, int appId) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        try {

            try {
                Result<PendantServiceProto.ResponseGetPendants> result = hyPendantService.getPendants(ids);
                if (RpcResult.isFail(result)) {
                    log.warn("hy getPendants fail. rCode={},ids={}", result.rCode(), ids);
                    return Collections.emptyList();
                }

                return PendantConvert.I.toPendantDtoListHy(result.target().getPendantsList());

            } catch (Exception e) {
                log.error("HyPendantRemote getByIds error, ids: {}, appId: {}", ids, appId, e);
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("HyPendantRemote getByIds error, ids: {}, appId: {}", ids, appId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Result<Void> updatePendantShowTime(PendantShowTimeParamDTO param) {
        PendantProbuf pendantProbuf = PendantProbuf.newBuilder()
                .setId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .build();
        Result<Void> result = hyPendantService.updatePendant(pendantProbuf);
        if (RpcResult.isFail(result)) {
            log.warn("hy updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return RpcResult.fail(UPDATE_PENDANT_SHOW_TIME_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updatePendantStatus(PendantStatusParamDTO param) {
        Result<Void> result = hyPendantService.updatePendantStatus(param.getPendantId(), param.getStatus().getStatus());
        if (RpcResult.isFail(result)) {
            log.warn("hy updatePendantStatus fail. rCode={},pendantId={},status={}", result.rCode(), param.getPendantId(), param.getStatus());
            return RpcResult.fail(UPDATE_PENDANT_STATUS_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<List<PendantDto>> getPendantWithConditions(List<Long> pendantIds) {

        Result<PendantServiceProto.ResponseGetAllStatusPendantById> result =
                hyPendantService.getAllStatusPendantById(PendantServiceProto.AllStatusPendantQueryParam.newBuilder().setPendantId(pendantIds.get(0)).build());
        if (RpcResult.isFail(result)) {
            log.warn("hy getPendantWithConditions fail. rCode={},pendantIds={}", result.rCode(), pendantIds);
            return RpcResult.fail(GET_PENDANT_WITH_CONDITIONS_FAIL);
        }
        return RpcResult.success(Lists.newArrayList(PendantConvert.I.toPendantConditionDtoHy(result.target().getPendant())));
    }

}
