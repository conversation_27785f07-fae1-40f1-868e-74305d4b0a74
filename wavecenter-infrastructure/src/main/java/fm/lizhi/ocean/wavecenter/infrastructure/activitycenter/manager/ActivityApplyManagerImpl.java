package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.constants.ActivityResourceTransferStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityApplyDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityMaterielConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.*;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityApplyInfoExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityFlowResourceGiveRecordExtMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyFlowResourceMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityApplyInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityResourceTransferMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.po.ActivityUpdatePo;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityBigClassMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityClassConfigMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityResourceConfigMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityApplyManagerImpl implements ActivityApplyManager {

    @Autowired
    private ActivityResourceManager activityResourceManager;

    @Autowired
    private ActivityApplyDao applyDao;

    @Autowired
    private ActivityApplyInfoExtraMapper infoExtraMapper;

    @Autowired
    private ActivityApplyInfoMapper activityApplyInfoMapper;

    @Autowired
    private IdManager idManager;

    @Autowired
    private ActivityApplyFlowResourceMapper activityApplyFlowResourceMapper;

    @Autowired
    private ActivityApplyProcessDao activityApplyProcessDao;

    @Autowired
    private ActivityTemplateUsedRelationDao activityTemplateUsedRelationDao;

    @Autowired
    private ActivityClassConfigMapper activityClassConfigMapper;

    @Autowired
    private ActivityBigClassMapper activityBigClassMapper;

    @Autowired
    private ActivityResourceTransferMapper activityResourceTransferMapper;

    @Autowired
    private ActivityResourceConfigMapper activityResourceConfigMapper;

    @Autowired
    private ActivityLevelManager activityLevelManager;

    @Autowired
    private ActivityResourceGiveDao resourceGiveDao;

    @Autowired
    private ActivityMaterielConvert activityMaterielConvert;

    @Autowired
    private ActivityFlowResourceGiveRecordExtMapper flowResourceGiveExtMapper;

    @Autowired
    private ActivityApplyDecorateDao activityApplyDecorateDao;

    @Autowired
    private ActivityApplyInfoExtMapper activityApplyInfoExtMapper;


    @Override
    public ActivityInfoDTO getActivityApplyInfoById(Long activityId) {
        ActivityApplyInfo info = applyDao.getActivityInfoById(activityId);
        return ActivityApplyConvert.I.activityInfo2DTO(info);
    }


    @Override
    public Integer getApplyCountByWeekly(Integer appId, Long njId, Date startTime) {
        Date beginOfWeek = MyDateUtil.beginOfWeek(startTime);
        Date endOfWeek = MyDateUtil.endOfWeek(startTime);
        log.info("getApplyCountByWeekly,beginOfWeek={},endOfWeek={}", beginOfWeek, endOfWeek);

        long count = applyDao.countApplyInWeek(appId, njId, beginOfWeek, endOfWeek);
        return Math.toIntExact(count);
    }

    @Override
    public List<ActivityApplyDecorateDTO> getDecorateListByActivityId(Long activityId) {

        List<ActivityApplyDecorate> decorates = activityApplyDecorateDao.getDecorates(activityId);
        if (CollectionUtils.isNotEmpty(decorates)) {
            return ActivityApplyDecorateConvert.I.entitysToDTOs(decorates);
        }

        ActivityApplyInfo activity = applyDao.getActivityInfoById(activityId);
        if (activity == null) {
            return Collections.emptyList();
        }

        List<ActivityApplyDecorateDTO> decorateList = new ArrayList<>();
        Long roomBackgroundId = activity.getRoomBackgroundId();
        if (roomBackgroundId != null && roomBackgroundId > 0) {
            ActivityApplyDecorateDTO dto = new ActivityApplyDecorateDTO();
            dto.setAppId(activity.getAppId());
            dto.setActivityId(activityId);
            dto.setDecorateId(roomBackgroundId);
            dto.setDecorateType(PlatformDecorateTypeEnum.BACKGROUND.getType());
            decorateList.add(dto);
        }

        Long avatarWidgetId = activity.getAvatarWidgetId();
        if (avatarWidgetId != null && avatarWidgetId > 0) {
            ActivityApplyDecorateDTO dto = new ActivityApplyDecorateDTO();
            dto.setAppId(activity.getAppId());
            dto.setActivityId(activityId);
            dto.setDecorateId(avatarWidgetId);
            dto.setDecorateType(PlatformDecorateTypeEnum.AVATAR.getType());
            decorateList.add(dto);
        }

        return decorateList;
    }

    @Override
    public Result<Boolean> activityApplyDateSave(ActivityApplyParamDTO params) {
        // 获取活动ID
        long activityId = idManager.genId();
        // 构建活动申请信息实体
        ActivityApplyInfo applyInfo = ActivityApplyConvert.I.applyInfoDto2Bean(params);
        applyInfo.setId(activityId);

        // 构建装扮
        List<ActivityApplyDecorate> decorates = buildDecorate(params.getRoomBackgroundIds()
                , params.getAvatarWidgetIds(), params.getRoomMarkId(), params.getAppId(), activityId);

        // 构建流量资源实体
        List<ActivityApplyFlowResource> flowResources = ActivityApplyConvert.I
                .flowResourceDtoList2Beans(params.getFlowResources());
        if (CollUtil.isNotEmpty(flowResources)) {
            flowResources.forEach(flowResource -> flowResource.setActivityId(activityId));
        }

        // 构建活动流程实体
        List<ActivityApplyProcess> processList = ActivityApplyConvert.I
                .processDtoList2BeanList(params.getProcessList());
        if (CollUtil.isNotEmpty(processList)) {
            processList.forEach(process -> process.setActivityId(activityId));
        }

        // 构建模板关联关系实体
        ActivityTemplateUsedRelation relation = ActivityTemplateUsedRelation.builder().activityId(activityId)
                .templateId(params.getTemplateId()).build();
        // 官频位时间表
        List<ActivityOfficialSeatTime> seatTimeList = buildSeatTimeList(flowResources);

        // 保存数据，事务操作
        try {
            applyDao.saveActivityApplyData(applyInfo, decorates, flowResources, processList, relation, seatTimeList,
                    params.getMaxSeatCount());
            return RpcResult.success();
        } catch (Exception e) {
            log.error("activityApplyDateSave error: applicantUid:{}, activityName:{}", params.getApplicantUid(),
                    params.getName(), e);
            return RpcResult.fail(ACTIVITY_APPLY_DATE_SAVE_FAIL, e.getMessage());
        }
    }

    private List<ActivityApplyDecorate> buildDecorate(List<Long> roomBackgroundIds
            , List<Long> avatarWidgetIds
            , Long roomMarkId, Integer appId
            , Long activityId){
        List<ActivityApplyDecorate> decorates = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roomBackgroundIds)) {
            for (Long roomBackgroundId : roomBackgroundIds) {
                ActivityApplyDecorate decorate = new ActivityApplyDecorate();
                decorate.setAppId(appId);
                decorate.setActivityId(activityId);
                decorate.setDecorateId(roomBackgroundId);
                decorate.setDecorateType(PlatformDecorateTypeEnum.BACKGROUND.getType());
                decorate.setDeleted(0);
                decorate.setModifyTime(new Date());
                decorate.setCreateTime(new Date());
                decorates.add(decorate);
            }
        }
        if (CollectionUtils.isNotEmpty(avatarWidgetIds)) {
            for (Long avatarWidgetId : avatarWidgetIds) {
                ActivityApplyDecorate decorate = new ActivityApplyDecorate();
                decorate.setAppId(appId);
                decorate.setActivityId(activityId);
                decorate.setDecorateId(avatarWidgetId);
                decorate.setDecorateType(PlatformDecorateTypeEnum.AVATAR.getType());
                decorate.setDeleted(0);
                decorate.setModifyTime(new Date());
                decorate.setCreateTime(new Date());
                decorates.add(decorate);
            }
        }

        // 角标
        if (roomMarkId != null) {
            ActivityApplyDecorate decorate = new ActivityApplyDecorate();
            decorate.setAppId(appId);
            decorate.setActivityId(activityId);
            decorate.setDecorateId(roomMarkId);
            decorate.setDecorateType(PlatformDecorateTypeEnum.ROOM_MARK.getType());
            decorate.setDeleted(0);
            decorate.setModifyTime(new Date());
            decorate.setCreateTime(new Date());
            decorates.add(decorate);
        }
        return decorates;
    }

    @Override
    public Result<Void> modifyActivityApply(ActivityApplyParamDTO applyParamDTO,
                                            ActivityFlowResourceDTO oldOfficialSeatResource) {
        Long activityId = applyParamDTO.getActivityId();
        // 构建活动申请信息实体
        ActivityApplyInfo applyInfo = ActivityApplyConvert.I.applyInfoDto2Bean(applyParamDTO);
        applyInfo.setId(activityId);

        // 构建装扮
        List<ActivityApplyDecorate> decorates = buildDecorate(applyParamDTO.getRoomBackgroundIds()
                , applyParamDTO.getAvatarWidgetIds(), applyParamDTO.getRoomMarkId(), applyParamDTO.getAppId(), activityId);

        // 构建流量资源实体
        List<ActivityApplyFlowResource> flowResources = ActivityApplyConvert.I
                .flowResourceDtoList2Beans(applyParamDTO.getFlowResources());
        if (CollUtil.isNotEmpty(flowResources)) {
            flowResources.forEach(flowResource -> flowResource.setActivityId(activityId));
        }
        // 修改的官频位时间表
        List<ActivityOfficialSeatTime> seatTimeList = buildSeatTimeList(flowResources);
        // 构建旧的官频位时间表
        List<ActivityOfficialSeatTime> oldSeatTimeList = buildOldSeatTimeList(oldOfficialSeatResource);


        // 构建活动流程实体
        List<ActivityApplyProcess> processList = ActivityApplyConvert.I
                .processDtoList2BeanList(applyParamDTO.getProcessList());
        if (CollUtil.isNotEmpty(processList)) {
            processList.forEach(process -> process.setActivityId(activityId));
        }

        // 构建模板关联关系
        ActivityTemplateUsedRelation relation = null;
        if (applyParamDTO.getTemplateId() != null) {
            relation = ActivityTemplateUsedRelation.builder().activityId(activityId)
                    .templateId(applyParamDTO.getTemplateId()).build();
        }

        // 构建更新实体
        ActivityUpdatePo activityUpdatePo = new ActivityUpdatePo();
        activityUpdatePo.setApplyInfo(applyInfo)
                .setDecorates(decorates)
                .setFlowResources(flowResources)
                .setProcessList(processList)
                .setSeatTimeList(seatTimeList)
                .setOldSeatTimeList(oldSeatTimeList)
                .setMaxSeatCount(applyParamDTO.getMaxSeatCount())
                .setRelation(relation);

        //如果是审批通过，还要构建发放记录
        ActivityApplyInfo oldApplyInfo = applyDao.getActivityInfoById(activityId);
        activityUpdatePo.setOldApplyInfo(oldApplyInfo);
        if (Objects.equals(oldApplyInfo.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
            // 设置不可修改信息
            setUnmodifiableInfo(activityUpdatePo, oldApplyInfo);
            //构建发放记录
            List<ActivityResourceGiveRecord> oldDressUpGiveRecordList = resourceGiveDao.getDressUpResourceGiveRecordList(applyParamDTO.getActivityId());
            boolean hasDressUpGiveRecord = CollectionUtils.isNotEmpty(oldDressUpGiveRecordList);
            List<Long> giveIds = hasDressUpGiveRecord ? oldDressUpGiveRecordList.stream().map(ActivityResourceGiveRecord::getId).collect(Collectors.toList()) : Collections.emptyList();

            List<ActivityFlowResourceGiveRecord> flowResourceGiveList = flowResourceGiveExtMapper.getFlowResourcesGiveRecordList(activityId, ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus());
            //取出flowResourceGiveList的giveId列表
            List<Long> flowResourceGiveIds = CollectionUtils.isNotEmpty(flowResourceGiveList) ? flowResourceGiveList.stream().map(ActivityFlowResourceGiveRecord::getGiveId).collect(Collectors.toList()) : Collections.emptyList();

            Pair<List<ActivityResourceGiveRecord>, List<ActivityDressUpGiveRecord>> dressRecordPair = activityMaterielConvert.buildDressUpResources(applyInfo, decorates);
            List<Long> oldNjIdList = getNjIdList(oldApplyInfo.getAccompanyNjIds(), oldApplyInfo.getHostId());

            // 取出 gift 类型的记录
            List<ActivityResourceGiveRecord> oldGiftGiveRecordList = resourceGiveDao.getResourceGiveRecordListByType(applyParamDTO.getActivityId(), ActivityResourceTypeEnum.GIFT);
            boolean hasGiftGiveRecord = CollectionUtils.isNotEmpty(oldGiftGiveRecordList);
            List<Long> oldGiftGiveIds = hasGiftGiveRecord ? oldGiftGiveRecordList.stream().map(ActivityResourceGiveRecord::getId).collect(Collectors.toList()) : Collections.emptyList();

            activityUpdatePo.setDressRecordPair(dressRecordPair)
                    .setOldNjIdList(oldNjIdList)
                    .setFlowResourceGiveIds(flowResourceGiveIds)
                    .setGiveIds(giveIds)
                    .setOldGiftGiveIds(oldGiftGiveIds)
            ;
        }

        try {
            applyDao.updateActivityApply(activityUpdatePo);
            return RpcResult.success();
        } catch (Exception e) {
            log.error("modifyActivityApply error: activityId:{}, activityName:{}", activityId, applyInfo.getName(), e);
            return RpcResult.fail(ACTIVITY_APPLY_DATE_SAVE_FAIL, e.getMessage());
        }
    }

    private List<ActivityOfficialSeatTime> buildOldSeatTimeList(ActivityFlowResourceDTO oldOfficialSeatResource) {
        ActivityApplyFlowResource oldFlowResource = ActivityApplyConvert.I
                .flowResourceDto2Bean(oldOfficialSeatResource);
        return oldFlowResource != null ? buildSeatTimeList(Collections.singletonList(oldFlowResource))
                : Collections.emptyList();
    }

    @Override
    public boolean existSameTimeAndHallActivity(Date startTime, Date endTime, Long njId) {
        List<Integer> auditStatusList = Arrays.asList(ActivityAuditStatusEnum.WAITING_AUDIT.getStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus());
        Integer count = infoExtraMapper.getValidActivityCount(startTime, endTime, njId, auditStatusList);
        return count != null && count > 0;
    }

    @Override
    public List<Long> getSameTimeAndHallActivityId(Date startTime, Date endTime, Long njId) {
        List<Integer> auditStatusList = Arrays.asList(ActivityAuditStatusEnum.WAITING_AUDIT.getStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus());
        List<Long> ids = infoExtraMapper.getValidActivityIds(startTime, endTime, njId, auditStatusList);
        if (ids == null) {
            return new ArrayList<>();
        }
        return ids;
    }

    @Override
    public List<SendReportDataInfoDTO> querySendReportDataInfoList(int gapTimeMin) {
        //数据取值范围 endTime在(NOW-gapTimeMin, NOW]区间
        Date max = DateUtil.getCurrentTime();
        Date min = DateUtil.getMinuteBefore(max, gapTimeMin);

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        ActivityApplyInfoExample.Criteria criteria = example.createCriteria();
        criteria.andEndTimeGreaterThan(min)
                .andEndTimeLessThanOrEqualTo(max)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andNotifyReportStatusEqualTo(false)
        ;
        List<ActivityApplyInfo> pageList = activityApplyInfoMapper.selectByExample(example);
        return ActivityApplyConvert.I.convertSendReportDataInfo(pageList);
    }

    @Override
    public Long getActivityLevelById(Long activityId) {
        return infoExtraMapper.getActivityLevelById(activityId);
    }

    @Override
    public ActivityInfoDTO getActivityInfoById(Long id) {
        ActivityApplyInfo info = applyDao.getActivityInfoById(id);
        return ActivityApplyConvert.I.activityInfo2DTO(info);
    }

    @Override
    public List<ActivityInfoDTO> getActivityInfoByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<ActivityApplyInfo> list = applyDao.getActivityInfoByIds(ids);
        return ActivityApplyConvert.I.activityInfos2DTOs(list);

    }

    @Override
    public List<ActivityInfoDTO> getActivityInfoByStartTimeGte(Date startTime) {
        List<ActivityApplyInfo> pageList = applyDao.getActivityInfoByStartTimeGte(startTime);
        return ActivityApplyConvert.I.activityInfos2DTOs(pageList);
    }

    @Override
    public List<ActivityFlowResourceDTO> getActivityFlowResourceByActivityIds(List<Long> activityIds) {

        if (CollUtil.isEmpty(activityIds)) {
            return Collections.emptyList();
        }

        ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
        example.createCriteria().andActivityIdIn(activityIds);
        List<ActivityApplyFlowResource> flowResources = activityApplyFlowResourceMapper.selectByExample(example);

        return ActivityApplyConvert.I.convertFlowResourceBean2DTO(flowResources);
    }

    @Override
    public List<ActivityFlowResourceDTO> getActivityFlowResourceByActivityId(Long activityId) {

        ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        List<ActivityApplyFlowResource> flowResources = activityApplyFlowResourceMapper.selectByExample(example);
        return ActivityApplyConvert.I.convertFlowResourceBean2DTO(flowResources);
    }

    @Override
    public List<ActivityProcessDTO> getActivityProcessByActivityId(Long activityId) {
        List<ActivityApplyProcess> processes = activityApplyProcessDao.getActivityProcessByActivityId(activityId);
        return ActivityApplyConvert.I.activityApplyProcess2ActivityProcessDTO(processes);
    }

    @Override
    public Boolean deleteActivityApply(Long activityId) {
        return applyDao.deleteActivityApply(activityId);
    }

    @Override
    public List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForApp(
            Date beginDate, Date endDate, Integer appId) {

        List<ActivityApplyInfo> activityApplyInfos = getActivityApplyInfos(appId, beginDate, endDate);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }

        return buildActivityApplyDetail(activityApplyInfos, appId);
    }

    @Override
    public List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForAppByActivityIds(
            List<Long> activityIds, Integer appId) {

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andIdIn(activityIds);

        List<ActivityApplyInfo> activityApplyInfos = activityApplyInfoMapper.selectByExample(example);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }

        return buildActivityApplyDetail(activityApplyInfos, appId);
    }

    @Override
    public List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForAppByNjId(
            Long njId, Date beginDate, Date endDate, Integer appId) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andNjIdEqualTo(njId)
                .andStartTimeGreaterThanOrEqualTo(beginDate)
                .andEndTimeLessThanOrEqualTo(endDate);
        ;

        List<ActivityApplyInfo> activityApplyInfos = activityApplyInfoMapper.selectByExample(example);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }

        return buildActivityApplyDetail(activityApplyInfos, appId);
    }

    @Override
    public List<ActivityApplyInfoDTO> getActivityApplyInfoByNjId(Long njId, Date beginDate, Date endDate,
                                                                 Integer appId) {

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andNjIdEqualTo(njId)
                .andStartTimeLessThanOrEqualTo(beginDate)
                .andEndTimeGreaterThanOrEqualTo(endDate);

        List<ActivityApplyInfo> activityApplyInfos = activityApplyInfoMapper.selectByExample(example);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }
        return this.buildActivityApplyInfo(activityApplyInfos, appId);
    }

    @Override
    public List<ActivityApplyInfoSimpleDTO> getInTimeRangeActivityApply(RequestGetInTimeRangeActivityApply request) {
        List<ActivityApplyInfo> list = activityApplyInfoExtMapper.getInTimeRangeActivityApply(request.getAppId()
                , request.getNjId()
                , ActivityAuditStatusEnum.AUDIT_PASS.getStatus()
                , ConfigUtils.getEnvRequired().name()
                , request.getStartTimeBeforeMinute()
                , request.getEndTimeAfterMinute()
        );
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return ActivityApplyConvert.I.convertActivityApplyInfoSimpleDTOs(list);
    }

    @Override
    public List<ActivityApplyInfoSimpleDTO> getInTimeRangeActivityApplyByProgramme(Date startTime, Date endTime, Integer appId, Long resourceConfigId) {
        List<ActivityApplyInfo> list = activityApplyInfoExtMapper.getInTimeRangeActivityApplyByProgramme(appId, startTime, endTime,
                ActivityAuditStatusEnum.AUDIT_PASS.getStatus(),
                ActivityResourceGiveStatusEnum.SUCCESS.getStatus(),
                ConfigUtils.getEnvRequired().name(),
                resourceConfigId
        );

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return ActivityApplyConvert.I.convertActivityApplyInfoSimpleDTOs(list);
    }

    @Override
    public int batchUpdateSendReportStatus(List<Long> activityIds) {
        ActivityApplyInfo updated = new ActivityApplyInfo();
        updated.setNotifyReportStatus(true);

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria().andIdIn(activityIds).andNotifyReportStatusEqualTo(false);
        return activityApplyInfoMapper.updateByExample(updated, example);
    }

    @Override
    public List<ActivityApplyInfoDTO> getActivityApplyInfoWithTimeRange(Date beginDate, Date endDate, Integer appId) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andStartTimeLessThanOrEqualTo(endDate)
                .andEndTimeGreaterThanOrEqualTo(beginDate);
        List<ActivityApplyInfo> activityApplyInfos = activityApplyInfoMapper.selectByExample(example);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }
        return this.buildActivityApplyInfo(activityApplyInfos, appId);
    }

    /**
     * 获取活动提报信息
     *
     * @param appId     应用ID
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return 活动提报信息
     */
    private List<ActivityApplyInfo> getActivityApplyInfos(Integer appId, Date beginDate, Date endDate) {
        List<ActivityApplyInfo> activityApplyInfos = infoExtraMapper.selectForApp(
                ConfigUtils.getEnvRequired().name(), appId,
                LogicDeleteConstants.NOT_DELETED, ActivityAuditStatusEnum.AUDIT_PASS.getStatus(),
                beginDate, endDate);
        return (activityApplyInfos == null || activityApplyInfos.isEmpty()) ? Collections.emptyList()
                : activityApplyInfos;
    }

    /**
     * 活动提报详细信息，用于APP端查看
     *
     * @param activityApplyInfos 活动提报信息
     * @param appId              应用ID
     * @return 活动提报详细信息
     */
    private List<ActivityApplyDetailForAppDTO> buildActivityApplyDetail(
            List<ActivityApplyInfo> activityApplyInfos, Integer appId) {

        List<Long> activityIds = activityApplyInfos.stream()
                .map(ActivityApplyInfo::getId).collect(Collectors.toList());
        List<Long> classIds = activityApplyInfos.stream()
                .map(ActivityApplyInfo::getClassId).collect(Collectors.toList());

        ActivityApplyFlowResourceExample resourceExample = new ActivityApplyFlowResourceExample();
        resourceExample.createCriteria()
                .andActivityIdIn(activityIds)
                .andStatusEqualTo(ActivityResourceAuditStatusEnum.ABLE_GIVE.getStatus());
        List<ActivityApplyFlowResource> flowResources = activityApplyFlowResourceMapper
                .selectByExample(resourceExample);
        if (flowResources.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> resourceConfigIds = flowResources.parallelStream()
                .map(ActivityApplyFlowResource::getResourceConfigId)
                .collect(Collectors.toList());
        ActivityResourceConfigExample activityResourceConfigExample = new ActivityResourceConfigExample();
        activityResourceConfigExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andIdIn(resourceConfigIds)
                .andResourceCodeEqualTo(AutoConfigResourceEnum.PROGRAMME.getResourceCode());
        List<ActivityResourceConfig> activityResourceConfigs = activityResourceConfigMapper
                .selectByExample(activityResourceConfigExample);
        if (activityResourceConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        // 优化：使用 filter 方法替代迭代器移除元素
        List<Long> finalResourceConfigIds = activityResourceConfigs.parallelStream()
                .map(ActivityResourceConfig::getId).collect(Collectors.toList());
        flowResources.removeIf(resource -> !finalResourceConfigIds.contains(resource.getResourceConfigId()));

        // 获取资源
        List<String> imageResoures = flowResources.parallelStream()
                .map(ActivityApplyFlowResource::getImageUrl).collect(Collectors.toList());
        imageResoures.addAll(activityApplyInfos.parallelStream()
                .map(ActivityApplyInfo::getPosterUrl).collect(Collectors.toList()));
        ActivityResourceTransferExample resourceTransferExample = new ActivityResourceTransferExample();
        resourceTransferExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andSourceUriIn(imageResoures)
                .andStatusEqualTo(ActivityResourceTransferStatusEnum.SUCCESS.getStatus());
        List<ActivityResourceTransfer> resourceTransfers = activityResourceTransferMapper
                .selectByExample(resourceTransferExample);
        if (resourceTransfers.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取活动分类
        ActivityClassConfigExample classConfigExample = new ActivityClassConfigExample();
        classConfigExample.createCriteria().andAppIdEqualTo(appId).andIdIn(classIds);
        List<ActivityClassConfig> activityClassConfigs = activityClassConfigMapper.selectByExample(classConfigExample);
        if (activityClassConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> bigClassIds = activityClassConfigs.stream()
                .map(ActivityClassConfig::getBigClassId).collect(Collectors.toList());
        ActivityBigClassExample bigClassExample = new ActivityBigClassExample();
        bigClassExample.createCriteria().andAppIdEqualTo(appId).andIdIn(bigClassIds);
        List<ActivityBigClass> activityBigClasses = activityBigClassMapper.selectByExample(bigClassExample);
        if (activityBigClasses.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Long, ActivityBigClass> bigClassMap = activityBigClasses.parallelStream()
                .collect(Collectors.toMap(ActivityBigClass::getId, resource -> resource,
                        (existing, replacement) -> replacement));
        Map<Long, ActivityClassConfig> classConfigMap = activityClassConfigs.parallelStream()
                .collect(Collectors.toMap(ActivityClassConfig::getId, resource -> resource,
                        (existing, replacement) -> replacement));
        Map<Long, ActivityApplyFlowResource> flowResourceMap = flowResources.parallelStream()
                .collect(Collectors.toMap(ActivityApplyFlowResource::getActivityId, resource -> resource,
                        (existing, replacement) -> replacement));
        Map<String, ActivityResourceTransfer> resourceTransferMap = resourceTransfers.parallelStream()
                .collect(Collectors.toMap(ActivityResourceTransfer::getSourceUri, resource -> resource,
                        (existing, replacement) -> replacement));

        List<ActivityApplyDetailForAppDTO> result = new ArrayList<>();
        for (ActivityApplyInfo activityApplyInfo : activityApplyInfos) {
            ActivityApplyFlowResource activityApplyFlowResource = flowResourceMap.get(activityApplyInfo.getId());
            ActivityClassConfig activityClassConfig = classConfigMap.get(activityApplyInfo.getClassId());
            if (activityApplyFlowResource == null || activityClassConfig == null) {
                continue;
            }

            ActivityBigClass activityBigClass = bigClassMap.get(activityClassConfig.getBigClassId());
            if (activityBigClass == null) {
                continue;
            }

            if (StringUtils.isBlank(activityApplyFlowResource.getImageUrl())) {
                continue;
            }

            ActivityResourceTransfer posterUrlResource = resourceTransferMap.get(activityApplyInfo.getPosterUrl());
            ActivityResourceTransfer flowResource = resourceTransferMap.get(activityApplyFlowResource.getImageUrl());
            if (flowResource == null) {
                continue;
            }

            ActivityApplyDetailForAppDTO dto = new ActivityApplyDetailForAppDTO();
            dto.setId(activityApplyInfo.getId());
            dto.setName(activityApplyInfo.getName());
            dto.setNjId(activityApplyInfo.getNjId());
            dto.setStartTime(activityApplyInfo.getStartTime());
            dto.setEndTime(activityApplyInfo.getEndTime());
            if (StringUtils.isNotBlank(activityApplyInfo.getAccompanyNjIds())) {
                dto.setAccompanyNjIds(Arrays.stream(
                                activityApplyInfo.getAccompanyNjIds().split(","))
                        .map(Long::parseLong).collect(Collectors.toList()));
            } else {
                dto.setAccompanyNjIds(Collections.emptyList());
            }
            dto.setActivityTool(activityApplyInfo.getActivityTool());
            dto.setIntroduction(activityApplyInfo.getIntroduction());
            dto.setClassId(activityBigClass.getId());
            dto.setClassName(activityBigClass.getName());
            dto.setActivityBigClassType(activityBigClass.getType());
            dto.setPosterUrl(posterUrlResource == null ? "" : posterUrlResource.getTargetUri());
            dto.setFlowResourceImageUrl(flowResource.getTargetUri());

            result.add(dto);
        }

        return result;
    }

    /**
     * 活动提报简单信息
     *
     * @param activityApplyInfos 活动提报信息
     * @param appId              应用ID
     * @return 活动提报详细信息
     */
    private List<ActivityApplyInfoDTO> buildActivityApplyInfo(
            List<ActivityApplyInfo> activityApplyInfos, Integer appId) {

        List<Long> classIds = activityApplyInfos.stream()
                .map(ActivityApplyInfo::getClassId).collect(Collectors.toList());

        // 获取活动分类
        ActivityClassConfigExample classConfigExample = new ActivityClassConfigExample();
        classConfigExample.createCriteria().andAppIdEqualTo(appId).andIdIn(classIds);
        List<ActivityClassConfig> activityClassConfigs = activityClassConfigMapper.selectByExample(classConfigExample);

        if (activityClassConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> bigClassIds = activityClassConfigs.stream()
                .map(ActivityClassConfig::getBigClassId).collect(Collectors.toList());
        ActivityBigClassExample bigClassExample = new ActivityBigClassExample();
        bigClassExample.createCriteria().andAppIdEqualTo(appId).andIdIn(bigClassIds);
        List<ActivityBigClass> activityBigClasses = activityBigClassMapper.selectByExample(bigClassExample);

        if (activityBigClasses.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Long, ActivityBigClass> bigClassMap = activityBigClasses.parallelStream()
                .collect(Collectors.toMap(ActivityBigClass::getId, resource -> resource,
                        (existing, replacement) -> replacement));
        Map<Long, ActivityClassConfig> classConfigMap = activityClassConfigs.parallelStream()
                .collect(Collectors.toMap(ActivityClassConfig::getId, resource -> resource,
                        (existing, replacement) -> replacement));

        List<ActivityApplyInfoDTO> result = new ArrayList<>();
        for (ActivityApplyInfo activityApplyInfo : activityApplyInfos) {
            ActivityClassConfig activityClassConfig = classConfigMap.get(activityApplyInfo.getClassId());
            if (activityClassConfig == null) {
                continue;
            }

            ActivityApplyInfoDTO dto = new ActivityApplyInfoDTO();
            dto.setId(activityApplyInfo.getId());
            dto.setName(activityApplyInfo.getName());
            dto.setNjId(activityApplyInfo.getNjId());
            dto.setStartTime(activityApplyInfo.getStartTime());
            dto.setEndTime(activityApplyInfo.getEndTime());

            // 活动分类信息 - APP展示是用活动大类ID和名字展示
            ActivityBigClass activityBigClass = bigClassMap.get(activityClassConfig.getBigClassId());
            dto.setBigClassId(activityBigClass.getId());
            dto.setClassName(activityBigClass.getName());

            // 查询活动等级
            // 用活动分类的子分类去查活动等级信息
            Long classId = activityClassConfig.getId();
            dto.setClassId(classId);
            ActivityLevelConfigBean levelInfo = activityLevelManager.getLevelByClassId(appId, classId);
            if (levelInfo != null) {
                dto.setLevelId(levelInfo.getId());
                dto.setLevelName(levelInfo.getLevel());
            }
            result.add(dto);
        }
        return result;
    }

    private List<ActivityOfficialSeatTime> buildSeatTimeList(List<ActivityApplyFlowResource> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return Collections.emptyList();
        }

        List<Long> ids = flowResources.stream().map(ActivityApplyFlowResource::getResourceConfigId)
                .collect(Collectors.toList());
        List<ActivityResourceSimpleInfoDTO> resources = activityResourceManager.batchResourceByIds(ids);

        Optional<ActivityResourceSimpleInfoDTO> result = resources.stream().filter(resource -> Objects
                        .equals(resource.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findFirst();
        if (!result.isPresent()) {
            return Collections.emptyList();
        }

        ActivityResourceSimpleInfoDTO infoDTO = result.get();
        for (ActivityApplyFlowResource flowResource : flowResources) {
            if (!flowResource.getResourceConfigId().equals(infoDTO.getId())) {
                continue;
            }
            OfficialSeatExtraBean bean = ResourceExtraMapping.convertJsonToExtra(flowResource.getExtra(),
                    AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode());
            if (bean == null) {
                continue;
            }
            List<DateDTO> timeSlots = DateTimeUtils.divideTimeSlots(bean.getStartTime(), bean.getEndTime());
            List<ActivityOfficialSeatTime> list = new ArrayList<>();
            for (DateDTO timeSlot : timeSlots) {
                ActivityOfficialSeatTime seatTime = ActivityOfficialSeatTime.builder()
                        .appId(infoDTO.getAppId())
                        .seat(bean.getSeat())
                        .startTime(timeSlot.getStartTime())
                        .endTime(timeSlot.getEndTime())
                        .build();
                list.add(seatTime);
            }
            return list;
        }
        return Collections.emptyList();
    }

    /**
     * 获取需要新增装扮发放记录的用户ID
     *
     * @param accompanyNjIds 陪档主播ID
     * @param hostId         主持ID
     * @return 用户ID列表
     */
    private List<Long> getNjIdList(String accompanyNjIds, Long hostId) {
        Set<Long> njIdSet = new HashSet<>();
        if (StringUtils.isNotEmpty(accompanyNjIds)) {
            Arrays.stream(accompanyNjIds.split(","))
                    .map(Long::valueOf)
                    .forEach(njIdSet::add);
        }
        if (hostId != null) {
            njIdSet.add(hostId);
        }
        return new ArrayList<>(njIdSet);
    }

    @Override
    public Long getActivityTemplateIdByActivityId(Long activityId) {
        ActivityTemplateUsedRelation relation = activityTemplateUsedRelationDao
                .getTemplateUsedRelationByActivityId(activityId);
        return relation == null ? null : relation.getTemplateId();
    }

    /**
     * 设置活动不可修改的信息
     *
     * @param activityUpdatePo 活动更新PO
     * @param oldApplyInfo     原活动申请信息
     */
    private void setUnmodifiableInfo(ActivityUpdatePo activityUpdatePo, ActivityApplyInfo oldApplyInfo) {
        activityUpdatePo.getApplyInfo().setAuditStatus(ActivityAuditStatusEnum.AUDIT_PASS.getStatus());
        activityUpdatePo.getApplyInfo().setNjId(oldApplyInfo.getNjId());
    }
}
