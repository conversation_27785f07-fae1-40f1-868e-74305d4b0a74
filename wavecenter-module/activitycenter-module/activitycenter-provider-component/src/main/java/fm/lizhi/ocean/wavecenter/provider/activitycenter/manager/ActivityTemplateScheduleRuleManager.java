package fm.lizhi.ocean.wavecenter.provider.activitycenter.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.constants.ActivityTemplateRecurrenceEnum;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.convert.ActivityTemplateScheduleRuleConvert;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.datastore.ActivityTemplateScheduleRuleDao;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.model.dto.ActivityTemplateScheduleRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityTemplateScheduleRuleManager {

    @Autowired
    private ActivityTemplateScheduleRuleDao activityTemplateScheduleRuleDao;

    /**
     * 根据活动模板ID获取活动模板日程规则
     *
     * @param templateId
     * @return
     */
    public Optional<ActivityTemplateScheduleRuleDTO> getScheduleByTemplateId(Long templateId) {

        ActivityTemplateScheduleRule schedule = activityTemplateScheduleRuleDao.getScheduleByTemplateId(templateId);
        ActivityTemplateScheduleRuleDTO dto = ActivityTemplateScheduleRuleConvert.I.convertActivityTemplateScheduleRuleDTO(schedule);
        return Optional.ofNullable(dto);
    }

    /**
     * 校验活动模板日程规则
     *
     */
    public Boolean checkTemplateScheduleRule(Long templateId, Long startTime, Long endTime) {

        Optional<ActivityTemplateScheduleRuleDTO> optional = getScheduleByTemplateId(templateId);
        if (!optional.isPresent()){
            // 空配置，默认通过
            log.info("checkTemplateScheduleRule, templateId:{} not config, pass.", templateId);
            return true;
        }

        // 不在时间日期区间内
        ActivityTemplateScheduleRuleDTO rule = optional.get();
        if (rule.getStartDate() != null && rule.getEndDate() != null){
            boolean isInStartTime = DateUtil.isIn(new Date(startTime), rule.getStartDate(), rule.getEndDate());
            boolean isInEndTime = DateUtil.isIn(new Date(endTime), rule.getStartDate(), rule.getEndDate());
            log.info("checkTemplateScheduleRule, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                    templateId, startTime, endTime, isInStartTime, isInEndTime);
            if (!isInStartTime || !isInEndTime){
                log.info("checkTemplateScheduleRule is not in time limit, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                        templateId, startTime, endTime, isInStartTime, isInEndTime);
                return false;
            }
        }


        if (rule.getRecurrenceType().equals(ActivityTemplateRecurrenceEnum.DAILY.getType())){
            // 校验是否符合小时区间
            if (rule.getStartTime() != null && rule.getEndTime() != null){

                DateTime startDateTime = DateUtil.parseTimeToday(DateUtil.formatTime(new Date(startTime)));
                DateTime endDateTime = DateUtil.parseTimeToday(DateUtil.formatTime(new Date(endTime)));
                DateTime ruleStart = DateUtil.parseTimeToday(DateUtil.formatTime(rule.getStartTime()));
                DateTime ruleEnd = DateUtil.parseTimeToday(DateUtil.formatTime(rule.getEndTime()));

                boolean isInStartTime = DateUtil.isIn(startDateTime, ruleStart, ruleEnd);
                boolean isInEndTime = DateUtil.isIn(endDateTime, ruleStart, ruleEnd);

                log.info("checkTemplateScheduleRule recurrenceType:Daily, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                        templateId, startTime, endTime, isInStartTime, isInEndTime);

                if (!isInStartTime || !isInEndTime) {
                    log.info("checkTemplateScheduleRule recurrenceType:Daily is not in time limit, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                            templateId, startTime, endTime, isInStartTime, isInEndTime);
                    return false;
                }
            }
        }

        return true;
    }
}
