package fm.lizhi.ocean.wavecenter.provider.activitycenter.datastore;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityTemplateScheduleRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ActivityTemplateScheduleRuleDao {

    @Autowired
    private ActivityTemplateScheduleRuleMapper activityTemplateScheduleRuleMapper;


    /**
     * 根据活动模板ID获取活动模板日程规则
     *
     * @param templateId
     * @return
     */
    public ActivityTemplateScheduleRule getScheduleByTemplateId(Long templateId) {
        ActivityTemplateScheduleRule param = new ActivityTemplateScheduleRule();
        param.setTemplateId(templateId);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());

        return activityTemplateScheduleRuleMapper.selectOne(param);
    }



}
