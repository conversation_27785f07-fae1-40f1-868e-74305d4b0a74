package fm.lizhi.ocean.wavecenter.provider.activitycenter.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 * 活动模板日程规则
 *
 * <AUTHOR>
 * @date 2025-09-05 04:39:06
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateScheduleRuleDTO {
    /**
     * 模板日程规则ID
     */
    private Long id;

    /**
     * 活动模板ID，关联activity_template_info.id
     */
    private Long templateId;

    /**
     * 重复类型：0-不重复，1-每日
     */
    private Integer recurrenceType;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 每日开始时间
     */
    private Date startTime;

    /**
     * 每日结束时间
     */
    private Date endTime;

}