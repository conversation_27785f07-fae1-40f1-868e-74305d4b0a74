package fm.lizhi.ocean.wavecenter.provider.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.response.ResponseActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.service.ActivityTemplateScheduleRuleService;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.convert.ActivityTemplateScheduleRuleConvert;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.manager.ActivityTemplateScheduleRuleManager;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.model.dto.ActivityTemplateScheduleRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityTemplateScheduleRuleServiceImpl implements ActivityTemplateScheduleRuleService {

    @Autowired
    private ActivityTemplateScheduleRuleManager activityTemplateScheduleRuleManager;


    /**
     * 根据活动模板ID获取活动模板日程规则
     * @param templateId
     * @return
     */
    @Override
    public Result<ResponseActivityTemplateScheduleRule> getScheduleByTemplateId(int appId, Long templateId) {
        LogContext.addReqLog("templateId={}, appId={}", templateId, appId);
        LogContext.addResLog("templateId={}, appId={}", templateId, appId);

        return ResultHandler.handle(appId, () -> {
            Optional<ActivityTemplateScheduleRuleDTO> optional = activityTemplateScheduleRuleManager.getScheduleByTemplateId(templateId);
            return optional.map(ActivityTemplateScheduleRuleConvert.I::convertResponseActivityTemplateScheduleRule).map(RpcResult::success)
                    .orElseGet(() -> RpcResult.success(null));
        });
    }

    @Override
    public Result<Boolean> checkTemplateScheduleRule(int appId, Long templateId,  Long startTime, Long endTime) {
        LogContext.addReqLog("templateId={}, appId={}", templateId, appId);
        LogContext.addResLog("templateId={}, appId={}", templateId, appId);

        return ResultHandler.handle(appId, () -> RpcResult.success(activityTemplateScheduleRuleManager.checkTemplateScheduleRule(templateId, startTime, endTime)));
    }
}
