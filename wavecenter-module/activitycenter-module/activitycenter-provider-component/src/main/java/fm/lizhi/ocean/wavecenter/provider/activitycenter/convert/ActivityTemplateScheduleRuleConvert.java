package fm.lizhi.ocean.wavecenter.provider.activitycenter.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.response.ResponseActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.model.dto.ActivityTemplateScheduleRuleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, uses = {CommonConvert.class},
        imports = {ConfigUtils.class})
public interface ActivityTemplateScheduleRuleConvert {

    ActivityTemplateScheduleRuleConvert I = Mappers.getMapper(ActivityTemplateScheduleRuleConvert.class);


    ActivityTemplateScheduleRuleDTO convertActivityTemplateScheduleRuleDTO(ActivityTemplateScheduleRule schedule);

    ResponseActivityTemplateScheduleRule convertResponseActivityTemplateScheduleRule(ActivityTemplateScheduleRuleDTO dto);
}
