package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板-礼物任务表
 *
 * @date 2025-09-10 07:12:45
 */
@Table(name = "`activity_template_gift_task`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateGiftTask {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 礼物ID
     */
    @Column(name= "`gift_id`")
    private Long giftId;

    /**
     * 任务类型，0 自动下架，1自动上架
     */
    @Column(name= "`task_type`")
    private Integer taskType;

    /**
     * 业务任务 ID
     */
    @Column(name= "`biz_task_id`")
    private Long bizTaskId;

    /**
     * 状态,-1未知状态, 0 创建失败, 1 创建成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 业务ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", giftId=").append(giftId);
        sb.append(", taskType=").append(taskType);
        sb.append(", bizTaskId=").append(bizTaskId);
        sb.append(", status=").append(status);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}