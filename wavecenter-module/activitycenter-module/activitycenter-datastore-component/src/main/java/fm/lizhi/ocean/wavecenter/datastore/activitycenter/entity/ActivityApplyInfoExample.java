package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ActivityApplyInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public ActivityApplyInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfo.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andClassIdIsNull() {
            addCriterion("class_id is null");
            return (Criteria) this;
        }

        public Criteria andClassIdIsNotNull() {
            addCriterion("class_id is not null");
            return (Criteria) this;
        }

        public Criteria andClassIdEqualTo(Long value) {
            addCriterion("class_id =", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotEqualTo(Long value) {
            addCriterion("class_id <>", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdGreaterThan(Long value) {
            addCriterion("class_id >", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("class_id >=", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdLessThan(Long value) {
            addCriterion("class_id <", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdLessThanOrEqualTo(Long value) {
            addCriterion("class_id <=", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdIn(List<Long> values) {
            addCriterion("class_id in", values, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotIn(List<Long> values) {
            addCriterion("class_id not in", values, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdBetween(Long value1, Long value2) {
            addCriterion("class_id between", value1, value2, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotBetween(Long value1, Long value2) {
            addCriterion("class_id not between", value1, value2, "classId");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andApplyTypeIsNull() {
            addCriterion("apply_type is null");
            return (Criteria) this;
        }

        public Criteria andApplyTypeIsNotNull() {
            addCriterion("apply_type is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTypeEqualTo(Integer value) {
            addCriterion("apply_type =", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeNotEqualTo(Integer value) {
            addCriterion("apply_type <>", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeGreaterThan(Integer value) {
            addCriterion("apply_type >", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_type >=", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeLessThan(Integer value) {
            addCriterion("apply_type <", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("apply_type <=", value, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeIn(List<Integer> values) {
            addCriterion("apply_type in", values, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeNotIn(List<Integer> values) {
            addCriterion("apply_type not in", values, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeBetween(Integer value1, Integer value2) {
            addCriterion("apply_type between", value1, value2, "applyType");
            return (Criteria) this;
        }

        public Criteria andApplyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_type not between", value1, value2, "applyType");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIsNull() {
            addCriterion("audit_reason is null");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIsNotNull() {
            addCriterion("audit_reason is not null");
            return (Criteria) this;
        }

        public Criteria andAuditReasonEqualTo(String value) {
            addCriterion("audit_reason =", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotEqualTo(String value) {
            addCriterion("audit_reason <>", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonGreaterThan(String value) {
            addCriterion("audit_reason >", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonGreaterThanOrEqualTo(String value) {
            addCriterion("audit_reason >=", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLessThan(String value) {
            addCriterion("audit_reason <", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLessThanOrEqualTo(String value) {
            addCriterion("audit_reason <=", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLike(String value) {
            addCriterion("audit_reason like", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotLike(String value) {
            addCriterion("audit_reason not like", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIn(List<String> values) {
            addCriterion("audit_reason in", values, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotIn(List<String> values) {
            addCriterion("audit_reason not in", values, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonBetween(String value1, String value2) {
            addCriterion("audit_reason between", value1, value2, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotBetween(String value1, String value2) {
            addCriterion("audit_reason not between", value1, value2, "auditReason");
            return (Criteria) this;
        }

        public Criteria andContactIsNull() {
            addCriterion("contact is null");
            return (Criteria) this;
        }

        public Criteria andContactIsNotNull() {
            addCriterion("contact is not null");
            return (Criteria) this;
        }

        public Criteria andContactEqualTo(String value) {
            addCriterion("contact =", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotEqualTo(String value) {
            addCriterion("contact <>", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactGreaterThan(String value) {
            addCriterion("contact >", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactGreaterThanOrEqualTo(String value) {
            addCriterion("contact >=", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactLessThan(String value) {
            addCriterion("contact <", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactLessThanOrEqualTo(String value) {
            addCriterion("contact <=", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactLike(String value) {
            addCriterion("contact like", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotLike(String value) {
            addCriterion("contact not like", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactIn(List<String> values) {
            addCriterion("contact in", values, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotIn(List<String> values) {
            addCriterion("contact not in", values, "contact");
            return (Criteria) this;
        }

        public Criteria andContactBetween(String value1, String value2) {
            addCriterion("contact between", value1, value2, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotBetween(String value1, String value2) {
            addCriterion("contact not between", value1, value2, "contact");
            return (Criteria) this;
        }

        public Criteria andApplicantUidIsNull() {
            addCriterion("applicant_uid is null");
            return (Criteria) this;
        }

        public Criteria andApplicantUidIsNotNull() {
            addCriterion("applicant_uid is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantUidEqualTo(Long value) {
            addCriterion("applicant_uid =", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidNotEqualTo(Long value) {
            addCriterion("applicant_uid <>", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidGreaterThan(Long value) {
            addCriterion("applicant_uid >", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidGreaterThanOrEqualTo(Long value) {
            addCriterion("applicant_uid >=", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidLessThan(Long value) {
            addCriterion("applicant_uid <", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidLessThanOrEqualTo(Long value) {
            addCriterion("applicant_uid <=", value, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidIn(List<Long> values) {
            addCriterion("applicant_uid in", values, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidNotIn(List<Long> values) {
            addCriterion("applicant_uid not in", values, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidBetween(Long value1, Long value2) {
            addCriterion("applicant_uid between", value1, value2, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andApplicantUidNotBetween(Long value1, Long value2) {
            addCriterion("applicant_uid not between", value1, value2, "applicantUid");
            return (Criteria) this;
        }

        public Criteria andContactNumberIsNull() {
            addCriterion("contact_number is null");
            return (Criteria) this;
        }

        public Criteria andContactNumberIsNotNull() {
            addCriterion("contact_number is not null");
            return (Criteria) this;
        }

        public Criteria andContactNumberEqualTo(String value) {
            addCriterion("contact_number =", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotEqualTo(String value) {
            addCriterion("contact_number <>", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberGreaterThan(String value) {
            addCriterion("contact_number >", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberGreaterThanOrEqualTo(String value) {
            addCriterion("contact_number >=", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLessThan(String value) {
            addCriterion("contact_number <", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLessThanOrEqualTo(String value) {
            addCriterion("contact_number <=", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLike(String value) {
            addCriterion("contact_number like", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotLike(String value) {
            addCriterion("contact_number not like", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberIn(List<String> values) {
            addCriterion("contact_number in", values, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotIn(List<String> values) {
            addCriterion("contact_number not in", values, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberBetween(String value1, String value2) {
            addCriterion("contact_number between", value1, value2, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotBetween(String value1, String value2) {
            addCriterion("contact_number not between", value1, value2, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andHostIdIsNull() {
            addCriterion("host_id is null");
            return (Criteria) this;
        }

        public Criteria andHostIdIsNotNull() {
            addCriterion("host_id is not null");
            return (Criteria) this;
        }

        public Criteria andHostIdEqualTo(Long value) {
            addCriterion("host_id =", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdNotEqualTo(Long value) {
            addCriterion("host_id <>", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdGreaterThan(Long value) {
            addCriterion("host_id >", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("host_id >=", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdLessThan(Long value) {
            addCriterion("host_id <", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdLessThanOrEqualTo(Long value) {
            addCriterion("host_id <=", value, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdIn(List<Long> values) {
            addCriterion("host_id in", values, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdNotIn(List<Long> values) {
            addCriterion("host_id not in", values, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdBetween(Long value1, Long value2) {
            addCriterion("host_id between", value1, value2, "hostId");
            return (Criteria) this;
        }

        public Criteria andHostIdNotBetween(Long value1, Long value2) {
            addCriterion("host_id not between", value1, value2, "hostId");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsIsNull() {
            addCriterion("accompany_nj_ids is null");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsIsNotNull() {
            addCriterion("accompany_nj_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsEqualTo(String value) {
            addCriterion("accompany_nj_ids =", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsNotEqualTo(String value) {
            addCriterion("accompany_nj_ids <>", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsGreaterThan(String value) {
            addCriterion("accompany_nj_ids >", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsGreaterThanOrEqualTo(String value) {
            addCriterion("accompany_nj_ids >=", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsLessThan(String value) {
            addCriterion("accompany_nj_ids <", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsLessThanOrEqualTo(String value) {
            addCriterion("accompany_nj_ids <=", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsLike(String value) {
            addCriterion("accompany_nj_ids like", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsNotLike(String value) {
            addCriterion("accompany_nj_ids not like", value, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsIn(List<String> values) {
            addCriterion("accompany_nj_ids in", values, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsNotIn(List<String> values) {
            addCriterion("accompany_nj_ids not in", values, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsBetween(String value1, String value2) {
            addCriterion("accompany_nj_ids between", value1, value2, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andAccompanyNjIdsNotBetween(String value1, String value2) {
            addCriterion("accompany_nj_ids not between", value1, value2, "accompanyNjIds");
            return (Criteria) this;
        }

        public Criteria andGoalIsNull() {
            addCriterion("goal is null");
            return (Criteria) this;
        }

        public Criteria andGoalIsNotNull() {
            addCriterion("goal is not null");
            return (Criteria) this;
        }

        public Criteria andGoalEqualTo(String value) {
            addCriterion("goal =", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotEqualTo(String value) {
            addCriterion("goal <>", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalGreaterThan(String value) {
            addCriterion("goal >", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalGreaterThanOrEqualTo(String value) {
            addCriterion("goal >=", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLessThan(String value) {
            addCriterion("goal <", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLessThanOrEqualTo(String value) {
            addCriterion("goal <=", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLike(String value) {
            addCriterion("goal like", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotLike(String value) {
            addCriterion("goal not like", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalIn(List<String> values) {
            addCriterion("goal in", values, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotIn(List<String> values) {
            addCriterion("goal not in", values, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalBetween(String value1, String value2) {
            addCriterion("goal between", value1, value2, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotBetween(String value1, String value2) {
            addCriterion("goal not between", value1, value2, "goal");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIsNull() {
            addCriterion("auxiliary_prop_url is null");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIsNotNull() {
            addCriterion("auxiliary_prop_url is not null");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlEqualTo(String value) {
            addCriterion("auxiliary_prop_url =", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotEqualTo(String value) {
            addCriterion("auxiliary_prop_url <>", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlGreaterThan(String value) {
            addCriterion("auxiliary_prop_url >", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlGreaterThanOrEqualTo(String value) {
            addCriterion("auxiliary_prop_url >=", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLessThan(String value) {
            addCriterion("auxiliary_prop_url <", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLessThanOrEqualTo(String value) {
            addCriterion("auxiliary_prop_url <=", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLike(String value) {
            addCriterion("auxiliary_prop_url like", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotLike(String value) {
            addCriterion("auxiliary_prop_url not like", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIn(List<String> values) {
            addCriterion("auxiliary_prop_url in", values, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotIn(List<String> values) {
            addCriterion("auxiliary_prop_url not in", values, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlBetween(String value1, String value2) {
            addCriterion("auxiliary_prop_url between", value1, value2, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotBetween(String value1, String value2) {
            addCriterion("auxiliary_prop_url not between", value1, value2, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIsNull() {
            addCriterion("poster_url is null");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIsNotNull() {
            addCriterion("poster_url is not null");
            return (Criteria) this;
        }

        public Criteria andPosterUrlEqualTo(String value) {
            addCriterion("poster_url =", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotEqualTo(String value) {
            addCriterion("poster_url <>", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlGreaterThan(String value) {
            addCriterion("poster_url >", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlGreaterThanOrEqualTo(String value) {
            addCriterion("poster_url >=", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLessThan(String value) {
            addCriterion("poster_url <", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLessThanOrEqualTo(String value) {
            addCriterion("poster_url <=", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLike(String value) {
            addCriterion("poster_url like", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotLike(String value) {
            addCriterion("poster_url not like", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIn(List<String> values) {
            addCriterion("poster_url in", values, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotIn(List<String> values) {
            addCriterion("poster_url not in", values, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlBetween(String value1, String value2) {
            addCriterion("poster_url between", value1, value2, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotBetween(String value1, String value2) {
            addCriterion("poster_url not between", value1, value2, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andActivityToolIsNull() {
            addCriterion("activity_tool is null");
            return (Criteria) this;
        }

        public Criteria andActivityToolIsNotNull() {
            addCriterion("activity_tool is not null");
            return (Criteria) this;
        }

        public Criteria andActivityToolEqualTo(String value) {
            addCriterion("activity_tool =", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotEqualTo(String value) {
            addCriterion("activity_tool <>", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolGreaterThan(String value) {
            addCriterion("activity_tool >", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolGreaterThanOrEqualTo(String value) {
            addCriterion("activity_tool >=", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLessThan(String value) {
            addCriterion("activity_tool <", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLessThanOrEqualTo(String value) {
            addCriterion("activity_tool <=", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLike(String value) {
            addCriterion("activity_tool like", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotLike(String value) {
            addCriterion("activity_tool not like", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolIn(List<String> values) {
            addCriterion("activity_tool in", values, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotIn(List<String> values) {
            addCriterion("activity_tool not in", values, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolBetween(String value1, String value2) {
            addCriterion("activity_tool between", value1, value2, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotBetween(String value1, String value2) {
            addCriterion("activity_tool not between", value1, value2, "activityTool");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIsNull() {
            addCriterion("room_announcement is null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIsNotNull() {
            addCriterion("room_announcement is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementEqualTo(String value) {
            addCriterion("room_announcement =", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotEqualTo(String value) {
            addCriterion("room_announcement <>", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementGreaterThan(String value) {
            addCriterion("room_announcement >", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementGreaterThanOrEqualTo(String value) {
            addCriterion("room_announcement >=", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLessThan(String value) {
            addCriterion("room_announcement <", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLessThanOrEqualTo(String value) {
            addCriterion("room_announcement <=", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLike(String value) {
            addCriterion("room_announcement like", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotLike(String value) {
            addCriterion("room_announcement not like", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIn(List<String> values) {
            addCriterion("room_announcement in", values, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotIn(List<String> values) {
            addCriterion("room_announcement not in", values, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementBetween(String value1, String value2) {
            addCriterion("room_announcement between", value1, value2, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotBetween(String value1, String value2) {
            addCriterion("room_announcement not between", value1, value2, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlIsNull() {
            addCriterion("room_announcement_img_url is null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlIsNotNull() {
            addCriterion("room_announcement_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlEqualTo(String value) {
            addCriterion("room_announcement_img_url =", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlNotEqualTo(String value) {
            addCriterion("room_announcement_img_url <>", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlGreaterThan(String value) {
            addCriterion("room_announcement_img_url >", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("room_announcement_img_url >=", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlLessThan(String value) {
            addCriterion("room_announcement_img_url <", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlLessThanOrEqualTo(String value) {
            addCriterion("room_announcement_img_url <=", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlLike(String value) {
            addCriterion("room_announcement_img_url like", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlNotLike(String value) {
            addCriterion("room_announcement_img_url not like", value, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlIn(List<String> values) {
            addCriterion("room_announcement_img_url in", values, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlNotIn(List<String> values) {
            addCriterion("room_announcement_img_url not in", values, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlBetween(String value1, String value2) {
            addCriterion("room_announcement_img_url between", value1, value2, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImgUrlNotBetween(String value1, String value2) {
            addCriterion("room_announcement_img_url not between", value1, value2, "roomAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdIsNull() {
            addCriterion("room_background_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdIsNotNull() {
            addCriterion("room_background_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdEqualTo(Long value) {
            addCriterion("room_background_id =", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdNotEqualTo(Long value) {
            addCriterion("room_background_id <>", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdGreaterThan(Long value) {
            addCriterion("room_background_id >", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_background_id >=", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdLessThan(Long value) {
            addCriterion("room_background_id <", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdLessThanOrEqualTo(Long value) {
            addCriterion("room_background_id <=", value, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdIn(List<Long> values) {
            addCriterion("room_background_id in", values, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdNotIn(List<Long> values) {
            addCriterion("room_background_id not in", values, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdBetween(Long value1, Long value2) {
            addCriterion("room_background_id between", value1, value2, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdNotBetween(Long value1, Long value2) {
            addCriterion("room_background_id not between", value1, value2, "roomBackgroundId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdIsNull() {
            addCriterion("avatar_widget_id is null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdIsNotNull() {
            addCriterion("avatar_widget_id is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdEqualTo(Long value) {
            addCriterion("avatar_widget_id =", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdNotEqualTo(Long value) {
            addCriterion("avatar_widget_id <>", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdGreaterThan(Long value) {
            addCriterion("avatar_widget_id >", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("avatar_widget_id >=", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdLessThan(Long value) {
            addCriterion("avatar_widget_id <", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdLessThanOrEqualTo(Long value) {
            addCriterion("avatar_widget_id <=", value, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdIn(List<Long> values) {
            addCriterion("avatar_widget_id in", values, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdNotIn(List<Long> values) {
            addCriterion("avatar_widget_id not in", values, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdBetween(Long value1, Long value2) {
            addCriterion("avatar_widget_id between", value1, value2, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdNotBetween(Long value1, Long value2) {
            addCriterion("avatar_widget_id not between", value1, value2, "avatarWidgetId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Integer value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Integer value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Integer value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Integer value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Integer> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Integer> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Integer value1, Integer value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorIsNull() {
            addCriterion("audit_operator is null");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorIsNotNull() {
            addCriterion("audit_operator is not null");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorEqualTo(String value) {
            addCriterion("audit_operator =", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorNotEqualTo(String value) {
            addCriterion("audit_operator <>", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorGreaterThan(String value) {
            addCriterion("audit_operator >", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("audit_operator >=", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorLessThan(String value) {
            addCriterion("audit_operator <", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorLessThanOrEqualTo(String value) {
            addCriterion("audit_operator <=", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorLike(String value) {
            addCriterion("audit_operator like", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorNotLike(String value) {
            addCriterion("audit_operator not like", value, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorIn(List<String> values) {
            addCriterion("audit_operator in", values, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorNotIn(List<String> values) {
            addCriterion("audit_operator not in", values, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorBetween(String value1, String value2) {
            addCriterion("audit_operator between", value1, value2, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andAuditOperatorNotBetween(String value1, String value2) {
            addCriterion("audit_operator not between", value1, value2, "auditOperator");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(Integer value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(Integer value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(Integer value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(Integer value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(Integer value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(Integer value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<Integer> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<Integer> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(Integer value1, Integer value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(Integer value1, Integer value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusIsNull() {
            addCriterion("notify_report_status is null");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusIsNotNull() {
            addCriterion("notify_report_status is not null");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusEqualTo(Boolean value) {
            addCriterion("notify_report_status =", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusNotEqualTo(Boolean value) {
            addCriterion("notify_report_status <>", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusGreaterThan(Boolean value) {
            addCriterion("notify_report_status >", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("notify_report_status >=", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusLessThan(Boolean value) {
            addCriterion("notify_report_status <", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("notify_report_status <=", value, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusIn(List<Boolean> values) {
            addCriterion("notify_report_status in", values, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusNotIn(List<Boolean> values) {
            addCriterion("notify_report_status not in", values, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("notify_report_status between", value1, value2, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andNotifyReportStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("notify_report_status not between", value1, value2, "notifyReportStatus");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIsNull() {
            addCriterion("gift_ids is null");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIsNotNull() {
            addCriterion("gift_ids is not null");
            return (Criteria) this;
        }

        public Criteria andGiftIdsEqualTo(String value) {
            addCriterion("gift_ids =", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotEqualTo(String value) {
            addCriterion("gift_ids <>", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsGreaterThan(String value) {
            addCriterion("gift_ids >", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsGreaterThanOrEqualTo(String value) {
            addCriterion("gift_ids >=", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLessThan(String value) {
            addCriterion("gift_ids <", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLessThanOrEqualTo(String value) {
            addCriterion("gift_ids <=", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLike(String value) {
            addCriterion("gift_ids like", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotLike(String value) {
            addCriterion("gift_ids not like", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIn(List<String> values) {
            addCriterion("gift_ids in", values, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotIn(List<String> values) {
            addCriterion("gift_ids not in", values, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsBetween(String value1, String value2) {
            addCriterion("gift_ids between", value1, value2, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotBetween(String value1, String value2) {
            addCriterion("gift_ids not between", value1, value2, "giftIds");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIsNull() {
            addCriterion("room_mark_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIsNotNull() {
            addCriterion("room_mark_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdEqualTo(Long value) {
            addCriterion("room_mark_id =", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotEqualTo(Long value) {
            addCriterion("room_mark_id <>", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdGreaterThan(Long value) {
            addCriterion("room_mark_id >", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_mark_id >=", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdLessThan(Long value) {
            addCriterion("room_mark_id <", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdLessThanOrEqualTo(Long value) {
            addCriterion("room_mark_id <=", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIn(List<Long> values) {
            addCriterion("room_mark_id in", values, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotIn(List<Long> values) {
            addCriterion("room_mark_id not in", values, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdBetween(Long value1, Long value2) {
            addCriterion("room_mark_id between", value1, value2, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotBetween(Long value1, Long value2) {
            addCriterion("room_mark_id not between", value1, value2, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIsNull() {
            addCriterion("room_mark_url is null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIsNotNull() {
            addCriterion("room_mark_url is not null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlEqualTo(String value) {
            addCriterion("room_mark_url =", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotEqualTo(String value) {
            addCriterion("room_mark_url <>", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlGreaterThan(String value) {
            addCriterion("room_mark_url >", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlGreaterThanOrEqualTo(String value) {
            addCriterion("room_mark_url >=", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLessThan(String value) {
            addCriterion("room_mark_url <", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLessThanOrEqualTo(String value) {
            addCriterion("room_mark_url <=", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLike(String value) {
            addCriterion("room_mark_url like", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotLike(String value) {
            addCriterion("room_mark_url not like", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIn(List<String> values) {
            addCriterion("room_mark_url in", values, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotIn(List<String> values) {
            addCriterion("room_mark_url not in", values, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlBetween(String value1, String value2) {
            addCriterion("room_mark_url between", value1, value2, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotBetween(String value1, String value2) {
            addCriterion("room_mark_url not between", value1, value2, "roomMarkUrl");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_apply_info
     *
     * @mbg.generated do_not_delete_during_merge Fri Sep 05 15:20:18 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_apply_info
     *
     * @mbg.generated Fri Sep 05 15:20:18 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}