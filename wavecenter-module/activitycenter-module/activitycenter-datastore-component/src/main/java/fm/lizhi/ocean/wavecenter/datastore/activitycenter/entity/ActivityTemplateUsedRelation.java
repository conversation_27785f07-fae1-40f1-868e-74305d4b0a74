package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板使用关联表
 *
 * @date 2024-10-21 06:21:15
 */
@Table(name = "`activity_template_used_relation`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateUsedRelation {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", activityId=").append(activityId);
        sb.append("]");
        return sb.toString();
    }
}