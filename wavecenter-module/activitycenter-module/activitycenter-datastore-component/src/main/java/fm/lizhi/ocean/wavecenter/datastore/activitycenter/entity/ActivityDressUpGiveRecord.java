package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 装扮资源发放记录
 *
 * @date 2025-04-23 10:57:08
 */
@Table(name = "`activity_dress_up_give_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityDressUpGiveRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 发放记录ID
     */
    @Column(name= "`give_id`")
    private Long giveId;

    /**
     * 获得装扮的用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 装扮类型，1：背景，2：头像框
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 装扮ID
     */
    @Column(name= "`dress_up_id`")
    private Long dressUpId;

    /**
     * 0:待发放，1：发放失败，2：发放成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", giveId=").append(giveId);
        sb.append(", userId=").append(userId);
        sb.append(", type=").append(type);
        sb.append(", dressUpId=").append(dressUpId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}