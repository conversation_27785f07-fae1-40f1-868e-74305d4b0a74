package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板日程规则表
 *
 * @date 2025-09-08 05:52:42
 */
@Table(name = "`activity_template_schedule_rule`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateScheduleRule {
    /**
     * 模板日程规则ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动模板ID，关联activity_template_info.id
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 重复类型：0-不重复，1-每日
     */
    @Column(name= "`recurrence_type`")
    private Integer recurrenceType;

    /**
     * 开始日期
     */
    @Column(name= "`start_date`")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name= "`end_date`")
    private Date endDate;

    /**
     * 每日开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 每日结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", recurrenceType=").append(recurrenceType);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}