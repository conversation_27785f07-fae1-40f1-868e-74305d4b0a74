package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动报表-用户送礼表
 *
 * @date 2025-04-29 05:49:36
 */
@Table(name = "`activity_report_data_gift`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityReportDataGift {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private String id;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 提报厅厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 活动开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    @Column(name= "`user_name`")
    private String userName;

    /**
     * 送礼钻石数
     */
    @Column(name= "`all_income`")
    private Long allIncome;

    /**
     * 送礼魅力值
     */
    @Column(name= "`all_charm`")
    private Long allCharm;

    /**
     * 送礼比数
     */
    @Column(name= "`gift_cnt`")
    private Long giftCnt;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", allIncome=").append(allIncome);
        sb.append(", allCharm=").append(allCharm);
        sb.append(", giftCnt=").append(giftCnt);
        sb.append(", appId=").append(appId);
        sb.append("]");
        return sb.toString();
    }
}