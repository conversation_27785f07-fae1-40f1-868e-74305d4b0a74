package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动AI生成结果评分表
 *
 * @date 2025-05-15 05:02:35
 */
@Table(name = "`activity_ai_result_rate`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityAiResultRate {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 类型, 1-文字, 2-图片
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * ai任务批次id
     */
    @Column(name= "`serial_id`")
    private Long serialId;

    /**
     * 评分
     */
    @Column(name= "`score`")
    private Integer score;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 操作人
     */
    @Column(name= "`creator`")
    private String creator;

    /**
     * 意见
     */
    @Column(name= "`advice`")
    private String advice;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", type=").append(type);
        sb.append(", serialId=").append(serialId);
        sb.append(", score=").append(score);
        sb.append(", createTime=").append(createTime);
        sb.append(", creator=").append(creator);
        sb.append(", advice=").append(advice);
        sb.append("]");
        return sb.toString();
    }
}