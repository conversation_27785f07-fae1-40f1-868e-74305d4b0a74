package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 管频位厅数量记录
 *
 * @date 2024-10-11 07:26:46
 */
@Table(name = "`activity_official_seat_time`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityOfficialSeatTime {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 官频位
     */
    @Column(name= "`seat`")
    private Integer seat;

    /**
     * 日期
     */
    @Column(name= "`show_date`")
    private Date showDate;

    /**
     * 官频位展示开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 官频位展示结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 档期官频位厅数量
     */
    @Column(name= "`count`")
    private Integer count;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", seat=").append(seat);
        sb.append(", showDate=").append(showDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", count=").append(count);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append("]");
        return sb.toString();
    }
}