package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-10-11 10:41:08
 */
@Table(name = "`activity_apply_flow_resource`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityApplyFlowResource {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 资源配置ID
     */
    @Column(name= "`resource_config_id`")
    private Long resourceConfigId;

    /**
     * 图片url
     */
    @Column(name= "`image_url`")
    private String imageUrl;

    /**
     * 状态，0：待审核，1：不发放，2：可发放
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 扩展配置，如果官频，需要配置展示的开始结束时间
     */
    @Column(name= "`extra`")
    private String extra;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", resourceConfigId=").append(resourceConfigId);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", status=").append(status);
        sb.append(", extra=").append(extra);
        sb.append("]");
        return sb.toString();
    }
}