package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 流量资源发放记录
 *
 * @date 2025-04-23 10:57:08
 */
@Table(name = "`activity_flow_resource_give_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityFlowResourceGiveRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 配置记录ID
     */
    @Column(name= "`give_id`")
    private Long giveId;

    /**
     * 流量资源ID
     */
    @Column(name= "`image_url`")
    private String imageUrl;

    /**
     * 获得流量的用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 资源配置ID
     */
    @Column(name= "`resource_config_id`")
    private Long resourceConfigId;

    /**
     * 资源code码
     */
    @Column(name= "`resource_code`")
    private String resourceCode;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    @Column(name= "`deploy_type`")
    private Integer deployType;

    /**
     * 资源名称
     */
    @Column(name= "`resource_name`")
    private String resourceName;

    /**
     * 0:待发放，1：发放失败，2：发放成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 扩展字段
     */
    @Column(name= "`extra`")
    private String extra;

    /**
     * 业务记录ID
     */
    @Column(name= "`biz_record_id`")
    private Long bizRecordId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", giveId=").append(giveId);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", userId=").append(userId);
        sb.append(", resourceConfigId=").append(resourceConfigId);
        sb.append(", resourceCode=").append(resourceCode);
        sb.append(", deployType=").append(deployType);
        sb.append(", resourceName=").append(resourceName);
        sb.append(", status=").append(status);
        sb.append(", extra=").append(extra);
        sb.append(", bizRecordId=").append(bizRecordId);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}