package fm.lizhi.ocean.wavecenter.provider.user;

import fm.lizhi.common.dubbo.adapter.springboot.EnableServiceProvider;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/9 19:14
 */
@Configuration
@ComponentScan(basePackages = "fm.lizhi.ocean.wavecenter.provider.user")
public class WavecenterProviderUserAutoConfiguration {
}