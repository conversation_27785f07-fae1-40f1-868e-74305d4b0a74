package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageActionEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.WcNoticeConfigEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessageBatch;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.PlaceholderUtils;

import fm.lizhi.ocean.wavecenter.service.common.constants.WaveCenterChatQueueEnum;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 线下专区私信管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneChatManager {

    /**
     * 跳槽保护协议提交通知内容
     */
    private static final String PROTECTION_SUBMIT_MSG_CONTENT = "【${nickName}(${playerName})】您好，您所签约的公会上传了主播合作协议及账号实名信息证明，请立即前往创作服务中心查看并确认。\n >>立即确认";

    /**
     * 跳槽保护协议提交通知跳转链接
     */
    private static final String PROTECTION_SUBMIT_MSG_URL = "${waveCenterUrl}#/app/notification/list?action=${msgAction}&id=${msgId}&bizId=${bizId}&playerId=${playerId}";

    /**
     * 主播同意协议批量通知内容
     */
    private static final String PROTECTION_AGREE_MSG_CONTENT = "【${nickName}(${playerName})】已同意确认主播合作协议，该主播合作协议已生效。";

    /**
     * 主播拒绝协议批量通知内容
     */
    private static final String PROTECTION_REJECT_MSG_CONTENT = "【${nickName}(${playerName})】不同意确认您在创作服务中心-线下专区上传的主播合作协议，请主播沟通后重新上传。";

    /**
     * 主播同意协议WEB通知标题
     */
    private static final String PROTECTION_AGREE_WEB_TITLE = "协议上传确认";

    /**
     * 主播拒绝协议WEB通知标题
     */
    private static final String PROTECTION_REJECT_WEB_TITLE = "协议上传确认";

    @Autowired
    private ChatManager chatManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private RoleManager roleManager;


    /**
     * 发送协议提交通知
     */
    public void sendProtectionSubmitMsg(int appId, Long userId, Long bizId){
        // 获取用户实名认证信息获取用户名
        Optional<UserVerifyDataDTO> userVerifyData = getUserVerifyData(userId);
        String playerName = userVerifyData.map(UserVerifyDataDTO::getName)
                .filter(StrUtil::isNotBlank)
                .orElse(String.valueOf(userId));

        Map<Long, SimpleUserDto> userMap = getUserByIds(CollUtil.newArrayList(userId));
        SimpleUserDto userDto = MapUtil.get(userMap, userId, SimpleUserDto.class);

        // 先发送创作者服务中心消息
        Long msgId = null;
        try {
            msgId = waveCenterMessageManager.sendMessage(new RequestSendMessage()
                    .setSendUserId(1L)
                    .setAppId(appId)
                    .setTargetUserId(userId)
                    .setBizId(bizId)
                    .setAction(MessageActionEnum.CONFIRM_OFFLINE_CONTRACT)
                    .setType(WcNoticeConfigEnum.SIGN_APPLY.getCode())
                    .setContent("签约公会已上传线下合作协议，请确认！")
                    .setTitle("协议上传确认")
            );
        }catch (Exception e){
            log.warn("send wave center message fail, userId={}, bizId={}", userId, bizId, e);
        }

        // 发送私信
        try {
            // 获取私信内容模板并替换占位符
            String finalContent = PlaceholderUtils.replace(PROTECTION_SUBMIT_MSG_CONTENT, "playerName", playerName, "nickName", userDto != null ? userDto.getName() : "");

            // 构建跳转链接
            String waveCenterUrl = commonConfig.getBizConfig().getWaveCenterUrl();
            String targetUrl = PlaceholderUtils.replace(PROTECTION_SUBMIT_MSG_URL,
                "waveCenterUrl", waveCenterUrl,
                "msgAction", MessageActionEnum.CONFIRM_OFFLINE_CONTRACT.name(),
                "msgId", msgId != null ? String.valueOf(msgId) : "",
                "bizId", bizId != null ? String.valueOf(bizId) : "",
                "playerId", String.valueOf(userId)
            );
            
            // 发送私信
            chatManager.sendChatAsyncWithSkipWeb(userId, finalContent, targetUrl);
            
            log.info("Protection submit message sent successfully to user: {}, msgId: {}, bizId: {}", 
                userId, msgId, bizId);
        } catch (Exception e) {
            log.error("Failed to send protection submit message to user: {}, msgId: {}, bizId: {}", 
                userId, msgId, bizId, e);
            throw new RuntimeException("发送跳槽保护协议提交消息失败", e);
        }
    }


    /**
     * 批量发送主播处理协议通知
     * @param appId 应用ID
     * @param playerId 主播ID
     * @param bizId 业务ID
     * @param agreeStatus 同意状态
     */
    public void batchSendProtectionPlayerHandleMsg(int appId, Long playerId, Long bizId, ProtectionAgreeStatusEnums agreeStatus){

        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(bizId);
        if (protection == null) {
            log.warn("batchSendProtectionPlayerHandleMsg protection not found, bizId: {}", bizId);
            return;
        }

        // 获取接收者用户ID列表
        Set<Long> receiverUserIds = getReceiverUserIds(appId, protection);

        if (agreeStatus == ProtectionAgreeStatusEnums.AGREED) {
            batchSendProtectionAgreeMsg(appId, receiverUserIds, playerId, bizId);
        } else if (agreeStatus == ProtectionAgreeStatusEnums.REJECTED || agreeStatus == ProtectionAgreeStatusEnums.NOT_PROCESSED) {
            batchSendProtectionRejectMsg(appId, receiverUserIds, playerId, bizId);
        }
    }

    @NotNull
    private Set<Long> getReceiverUserIds(int appId, OfflineZoneProtection protection) {

        // 上传用户、厅主
        Set<Long> receiverUserIds = CollUtil.newHashSet(protection.getUploadUserId(), protection.getNjId());
        Optional<FamilyBean> family = familyManager.getFamily(appId, protection.getFamilyId());
        family.ifPresent(familyBean -> {
            // 家族长
            receiverUserIds.add(familyBean.getUserId());

            // 家族管理,授权用户
            List<Long> userIds = roleManager.getRoomRoleAuthUserIds(appId, familyBean.getUserId(), RoleEnum.FAMILY);
            if (CollUtil.isNotEmpty(userIds)){
                receiverUserIds.addAll(userIds);
            }
        });

        // 厅管理，授权用户
        List<Long> njIds = roleManager.getRoomRoleAuthUserIds(appId, protection.getNjId(), RoleEnum.ROOM);
        if (CollUtil.isNotEmpty(njIds)){
            receiverUserIds.addAll(njIds);
        }
        return receiverUserIds;
    }


    /**
     * 批量发送主播同意协议通知
     * @param appId 应用ID
     * @param receiverUserIds 接收者用户ID列表
     * @param playerId 主播ID
     * @param bizId 业务ID
     */
    public void batchSendProtectionAgreeMsg(int appId, Set<Long> receiverUserIds, Long playerId, Long bizId) {
        if (receiverUserIds == null || receiverUserIds.isEmpty()) {
            log.warn("batchSendProtectionAgreeMsg receiverUserIds is empty, playerId: {}", playerId);
            return;
        }

        // 获取主播实名认证信息
        Optional<UserVerifyDataDTO> userVerifyData = getUserVerifyData(playerId);
        String playerName = userVerifyData.map(UserVerifyDataDTO::getName)
                .filter(StrUtil::isNotBlank)
                .orElse(String.valueOf(playerId));

        Map<Long, SimpleUserDto> userMap = getUserByIds(CollUtil.newArrayList(playerId));
        SimpleUserDto userDto = MapUtil.get(userMap, playerId, SimpleUserDto.class);

        try {
            // 批量发送私信
            String finalContent = PlaceholderUtils.replace(PROTECTION_AGREE_MSG_CONTENT, "playerName", playerName, "nickName", userDto != null ? userDto.getName() : "");
            // 批量发送WEB通知
            RequestSendMessageBatch webRequest = new RequestSendMessageBatch()
                    .setSendUserId(1L)
                    .setAppId(appId)
                    .setTargetUserIds(new ArrayList<>(receiverUserIds))
                    .setBizId(bizId)
                    .setType(WcNoticeConfigEnum.SIGN_APPLY.getCode())
                    .setContent(finalContent)
                    .setTitle(PROTECTION_AGREE_WEB_TITLE);
            
            waveCenterMessageManager.sendMessageBatch(webRequest);

            chatManager.batchSendChatAsync(receiverUserIds, finalContent);
            
            log.info("Batch protection agree message sent successfully to users: {}, playerId: {}, bizId: {}", 
                receiverUserIds, playerId, bizId);
        } catch (Exception e) {
            log.error("Failed to batch send protection agree message to users: {}, playerId: {}, bizId: {}", 
                receiverUserIds, playerId, bizId, e);
        }
    }

    /**
     * 批量发送主播拒绝协议通知
     * @param appId 应用ID
     * @param receiverUserIds 接收者用户ID列表
     * @param playerId 主播ID
     * @param bizId 业务ID
     */
    public void batchSendProtectionRejectMsg(int appId, Set<Long> receiverUserIds, Long playerId, Long bizId) {
        if (receiverUserIds == null || receiverUserIds.isEmpty()) {
            log.warn("batchSendProtectionRejectMsg receiverUserIds is empty, playerId: {}", playerId);
            return;
        }

        // 获取主播实名认证信息
        Optional<UserVerifyDataDTO> userVerifyData = getUserVerifyData(playerId);
        String playerName = userVerifyData.map(UserVerifyDataDTO::getName)
                .filter(StrUtil::isNotBlank)
                .orElse(String.valueOf(playerId));

        Map<Long, SimpleUserDto> userMap = getUserByIds(CollUtil.newArrayList(playerId));
        SimpleUserDto userDto = MapUtil.get(userMap, playerId, SimpleUserDto.class);


        try {

            // 批量发送私信
            String finalContent = PlaceholderUtils.replace(PROTECTION_REJECT_MSG_CONTENT, "playerName", playerName, "nickName", userDto != null ? userDto.getName() : "");
            // 批量发送WEB通知
            RequestSendMessageBatch webRequest = new RequestSendMessageBatch()
                    .setSendUserId(1L)
                    .setAppId(appId)
                    .setTargetUserIds(new ArrayList<>(receiverUserIds))
                    .setBizId(bizId)
                    .setType(WcNoticeConfigEnum.SIGN_APPLY.getCode())
                    .setContent(finalContent)
                    .setTitle(PROTECTION_REJECT_WEB_TITLE);
            
            waveCenterMessageManager.sendMessageBatch(webRequest);
            
            chatManager.batchSendChatAsync(receiverUserIds, finalContent);
            
            log.info("Batch protection reject message sent successfully to users: {}, playerId: {}, bizId: {}", 
                receiverUserIds, playerId, bizId);
        } catch (Exception e) {
            log.error("Failed to batch send protection reject message to users: {}, playerId: {}, bizId: {}", 
                receiverUserIds, playerId, bizId, e);
        }
    }

    /**
     * 获取用户实名认证信息
     */
    private Optional<UserVerifyDataDTO> getUserVerifyData(Long userId) {
        Optional<UserVerifyDataDTO> verifyDataOp = userManager.getVerifyData(userId);
        if (!verifyDataOp.isPresent()) {
            log.warn("getUserVerifyData failed, userId:{}", userId);
            return Optional.empty();
        }
        return verifyDataOp;
    }


    private Map<Long, SimpleUserDto> getUserByIds(List<Long> ids) {
        return userManager.getSimpleUserMapByIds(ids);
    }

}
