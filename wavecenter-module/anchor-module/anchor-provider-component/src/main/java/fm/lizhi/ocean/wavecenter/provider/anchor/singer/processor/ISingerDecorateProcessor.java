package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor;

import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 */
public interface ISingerDecorateProcessor extends BusinessEnvAwareProcessor {

    /**
     * 回收奖励
     *
     * @param appId      应用ID
     * @param singerId   歌手ID
     * @param singerType 歌手类型
     * @param operator   操作人
     */
    void recoverSingerAward(int appId, long singerId, int singerType, String operator);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerDecorateProcessor.class;
    }



    default RequestDeliverSingerAward buildRequest(int appId, long singerId, int singerType, SingerDecorateFlowOperateEnum operateType,
                                                   String reason, String operator) {
        RequestDeliverSingerAward request = new RequestDeliverSingerAward();
        request.setAppId(appId);
        request.setSingerId(singerId);
        request.setSingerType(singerType);
        request.setOperateType(operateType);
        request.setReason(reason);
        request.setOperator(operator);
        return request;
    }
}
