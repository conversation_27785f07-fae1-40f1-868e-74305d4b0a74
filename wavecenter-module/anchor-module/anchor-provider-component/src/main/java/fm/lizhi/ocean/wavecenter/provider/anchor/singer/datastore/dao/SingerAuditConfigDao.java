package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;


import java.util.List;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerAuditConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerAuditConfigExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerAuditConfigMapper;

@Component
public class SingerAuditConfigDao {

    @Autowired
    private SingerAuditConfigMapper singerAuditConfigMapper;

    /**
     * 获取主播审核配置
     * @param appId 应用ID
     * @return 主播审核配置
     */
    public List<SingerAuditConfig> getSingerAuditConfig(Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        SingerAuditConfigExample example = new SingerAuditConfigExample();
        example.createCriteria().andAppIdEqualTo(appId).andDeployEnvEqualTo(deployEnv);
        List<SingerAuditConfig> configs = singerAuditConfigMapper.selectByExample(example);
        return configs;
    }

    /**
     * 查询出启用状态的配置
     * @param appId 应用ID
     * @return 启用状态的配置   
     */
    public List<SingerAuditConfig> getEnableSingerAuditConfig(Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        SingerAuditConfigExample example = new SingerAuditConfigExample();
        example.createCriteria().andAppIdEqualTo(appId).andDeployEnvEqualTo(deployEnv).andEnabledEqualTo(Boolean.TRUE);
        return singerAuditConfigMapper.selectByExample(example);
    }

    /**
     * 查询出启用状态的配置
     * @param appId 应用ID
     * @return 启用状态的配置
     */
    public List<SingerAuditConfig> getSingerAuditConfigByScene(Integer appId, Integer auditScene) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        SingerAuditConfigExample example = new SingerAuditConfigExample();
        example.createCriteria().andAppIdEqualTo(appId).andDeployEnvEqualTo(deployEnv).andEnabledEqualTo(Boolean.TRUE).andAuditSceneEqualTo(auditScene);
        return singerAuditConfigMapper.selectByExample(example);
    }

    /**
     * 更新主播审核配置
     * @param config 主播审核配置
     * @return 是否更新成功
     */
    public boolean updateSingerAuditConfig(SingerAuditConfig config) {
        return singerAuditConfigMapper.updateByPrimaryKey(config) > 0;
    }
}
