package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.ISingerDecorateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmSingerDecorateProcessor implements ISingerDecorateProcessor {

    @Override
    public void recoverSingerAward(int appId, long singerId, int singerType, String operator) {
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
