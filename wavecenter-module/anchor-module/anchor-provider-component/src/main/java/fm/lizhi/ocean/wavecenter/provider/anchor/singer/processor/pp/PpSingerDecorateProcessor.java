package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateOperateService;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.ISingerDecorateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpSingerDecorateProcessor implements ISingerDecorateProcessor {


    @Autowired
    private SingerDecorateOperateService singerDecorateAwardOperate;

    @Override
    public void recoverSingerAward(int appId, long singerId, int singerType, String operator) {
        //回收奖励
        RequestDeliverSingerAward buildRequest = buildRequest(appId, singerId, singerType, SingerDecorateFlowOperateEnum.RECOVER, SingerDecorateOperateReasonConstant.SINGER_TYPE_CHANGE, operator);
        //回收奖励
        Result<Void> recoverRes = singerDecorateAwardOperate.singerDecorateAwardOperate(buildRequest);
        if (recoverRes.rCode() != 0) {
            log.error("recover singer award failed, singerId:{}, singerType:{}, rCode:{}", singerId, singerType, recoverRes.rCode());
        }
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
