package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import java.util.List;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerBlackList;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerBlackListDTO;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingerBlackListConvert {

    SingerBlackListConvert I = Mappers.getMapper(SingerBlackListConvert.class);

    List<SingerBlackListDTO> beanListtoDTOList(List<SingerBlackList> singerBlackList);

}
