package fm.lizhi.ocean.wavecenter.provider.anchor.singer.remote.pp;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.remote.IFamilySingHallApplyRemote;
import fm.pp.family.api.FamilySingHallApplyService;
import fm.pp.family.bean.family.sing.hall.apply.ReqQueryApplyRecordDto;
import fm.pp.family.bean.family.sing.hall.apply.ResQueryApplyRecordDto;
import fm.pp.family.constants.SingHallApplyAuditStatusConstant;
import fm.pp.family.protocol.FamilySingHallApplyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpFamilySingHallApplyRemote implements IFamilySingHallApplyRemote {

    @Autowired
    private FamilySingHallApplyService familySingHallApplyService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public boolean isInApply(Long njId){

        boolean isWait = queryApplyRecord(njId, SingHallApplyAuditStatusConstant.WAIT_AUDIT);
        if (!isWait){
            return queryApplyRecord(njId, SingHallApplyAuditStatusConstant.AUDIT_PASS);
        }
        return true;
    }

    private boolean queryApplyRecord(Long njId, int auditStatus) {
        ReqQueryApplyRecordDto request = new ReqQueryApplyRecordDto();
        request.setNjId(njId);
        request.setAuditStatus(auditStatus);
        request.setPage(1);
        request.setPageSize(10);

        Result<FamilySingHallApplyServiceProto.ResponseQueryApplyRecord> result =
                familySingHallApplyService.queryApplyRecord(JsonUtil.dumps(request));

        if (RpcResult.isFail(result)) {
            log.warn("pp query apply record fail, njId={}, rCode:{}", njId, result.rCode());
            return false;
        }

        return Optional.ofNullable(result.target()).map(FamilySingHallApplyServiceProto.ResponseQueryApplyRecord::getRspDto)
                .map(res -> JsonUtils.fromJsonString(res, ResQueryApplyRecordDto.class))
                .map(dto -> CollUtil.isNotEmpty(dto.getApplyRecords()))
                .orElse(false);
    }
}
