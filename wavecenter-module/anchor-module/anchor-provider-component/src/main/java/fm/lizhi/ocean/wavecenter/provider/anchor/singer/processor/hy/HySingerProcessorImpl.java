package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.hy;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HySingerProcessorImpl implements ISingerProcessor {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private FamilyManager familyManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public int importSinger(Map<Integer, List<Long>> ppImportSingerMap) {
        BusinessEvnEnum evnEnum = ContextUtils.getBusinessEvnEnum();
        int importedCount = 0;

        for (Map.Entry<Integer, List<Long>> entry : ppImportSingerMap.entrySet()) {
            Integer singerType = entry.getKey();
            List<Long> userIds = entry.getValue();

            if (CollUtil.isEmpty(userIds)) {
                continue;
            }

            // 校验歌手等级是否存在
            if (isInvalidSingerType(singerType, evnEnum)) {
                log.warn("PP 无效的歌手等级: {}", singerType);
                continue;
            }

            // 分组处理，每个分组最多处理20个用户
            List<List<Long>> userGroups = CollUtil.split(userIds, 20);

            for (List<Long> group : userGroups) {
                // 批量查询已存在的歌手
                Map<Long, SingerInfoDTO> existsMap = singerInfoManager.getSingerInfoByUserIds(group, evnEnum.getAppId(), null)
                        .stream().collect(Collectors.toMap(SingerInfoDTO::getUserId, Function.identity(), (e1, e2) -> e1));

                for (Long userId : group) {
                    if (existsMap.containsKey(userId)) {
                        log.info("PP 歌手已存在，跳过导入 userId: {}, singerType: {}", userId, singerType);
                        continue;
                    }

                    try {
                        boolean success = importSinger(userId, singerType, evnEnum);
                        importedCount += success ? 1 : 0;
                        if (success){
                            // 加入到缓存中，避免重复写入
                            existsMap.put(userId, new SingerInfoDTO());
                        }
                        log.info("PP 导入歌手 userId: {}, singerType: {}, success:{}", userId, singerType, success);
                    } catch (Exception e) {
                        log.error("PP 导入歌手失败 userId: {}, singerType: {}", userId, singerType, e);
                    }
                }
            }

        }
        return importedCount;
    }

    @Override
    public List<Long> getRelatedEliminateSingerIds(List<Long> userIds, int appId, List<Long> ids) {
        // hy 不需要关联淘汰
        return Collections.emptyList();
    }

    @Override
    public ResponseGetAllSingerStatics fillSingerStatics(Integer appId, ResponseGetAllSingerStatics result) {
        // hy 不展示
        return result
                .setSeniorSingerCnt(0)
                .setAllIncomeSeniorSingerCnt(0)
                .setOriginalSeniorSinger(0)
                .setAllIncomeOriginalSeniorSingerCnt(0)
                ;
    }

    @Override
    public List<SingerInfoDTO> getWaitAutoEliminateSingerInfo(Integer appId, long userId) {
        //黑叶只自动淘汰认证歌手，不自动淘汰高级歌手
        List<SingerInfoDTO> singerInfoByUserId = singerInfoManager.getSingerInfoByUserId(appId, userId, Lists.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING));
        //过滤出认证歌手记录或者认证中的歌手记录
        return singerInfoByUserId.stream()
        .filter(singerInfoDTO -> singerInfoDTO.getSingerType() == SingerTypeEnum.NEW.getType() 
            || singerInfoDTO.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus())
        .collect(Collectors.toList());
    }

    private boolean importSinger(Long userId, Integer singerType, BusinessEvnEnum evnEnum) {
        Optional<UserInFamilyBean> familyBeanOptional = Optional.ofNullable(familyManager.getUserInFamily(userId));
        SaveSingerInfoParamDTO param = new SaveSingerInfoParamDTO();
        param.setAppId(evnEnum.getAppId());
        param.setUserId(userId);
        param.setNjId(familyBeanOptional.map(UserInFamilyBean::getNjId).orElse(0L));
        param.setFamilyId(familyBeanOptional.map(UserInFamilyBean::getFamilyId).orElse(0L) );
        param.setSingerVerifyId(0L);
        param.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
        param.setSongStyle("");
        param.setOriginalSinger(false);
        param.setSingerType(singerType);
        param.setOperator(SingerOperatorConstant.SYSTEM);
        param.setContactNumber("");
        return singerInfoManager.saveSingerInfo(param);
    }
}
