package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.xm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.CommonSingerConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.pp.dto.vocal.GetVocalSingerVerifyListReq;
import xm.fm.lizhi.live.pp.dto.vocal.GetVocalSingerVerifyListResp;
import xm.fm.lizhi.live.pp.dto.vocal.VocalSingerVerifyDto;
import xm.fm.lizhi.live.pp.vocal.api.VocalSingerVerifyService;
import xm.fm.lizhi.live.pp.vocal.enums.MusicTypeEnum;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class XmSingerProcessorImpl implements ISingerProcessor {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private VocalSingerVerifyService vocalSingerVerifyService;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public int importSinger(Map<Integer, List<Long>> importSingerMap) {
        BusinessEvnEnum evnEnum = ContextUtils.getBusinessEvnEnum();
        int importedCount = 0;

        for (Map.Entry<Integer, List<Long>> entry : importSingerMap.entrySet()) {
            Integer singerType = entry.getKey();
            List<Long> userIds = entry.getValue();

            if (CollUtil.isEmpty(userIds)) {
                continue;
            }

            // 校验歌手等级是否存在
            if (isInvalidSingerType(singerType, evnEnum)) {
                log.warn("XM 无效的歌手等级: {}", singerType);
                continue;
            }

            // 分组处理，每个分组最多处理20个用户
            List<List<Long>> userGroups = CollUtil.split(userIds, 20);
            CommonSingerConfig config = singerAnchorConfig.getBizConfig(evnEnum.getAppId());
            Map<Integer, SongStyleBean> songStyleMap = new HashMap<>();
            //构建歌曲风格枚举
            if (config != null && config.getSongStyleConfig() != null) {
                String songStyleConfig = config.getSongStyleConfig();
                songStyleMap = JSON.parseArray(songStyleConfig, SongStyleBean.class)
                        .stream()
                        .filter(SongStyleBean::isEnabled)
                        .collect(Collectors.toMap(SongStyleBean::getId, Function.identity(), (e1, e2) -> e1));
            }

            for (List<Long> group : userGroups) {
                // 批量查询已存在的歌手
                Map<Long, SingerInfoDTO> existsMap = singerInfoManager.getSingerInfoByUserIds(group, evnEnum.getAppId(), null)
                        .stream().collect(Collectors.toMap(SingerInfoDTO::getUserId, Function.identity(), (e1, e2) -> e1));

                for (Long userId : group) {
                    if (existsMap.containsKey(userId)) {
                        log.info("XM 歌手已存在，跳过导入 userId: {}, singerType: {}", userId, singerType);
                        continue;
                    }

                    try {
                        boolean success = importSinger(userId, singerType, evnEnum , songStyleMap);
                        importedCount += success ? 1 : 0;
                        if (success){
                            // 加入到缓存中，避免重复写入
                            existsMap.put(userId, new SingerInfoDTO());
                        }
                        log.info("XM 导入歌手 userId: {}, singerType: {}, success:{}", userId, singerType, success);
                    } catch (Exception e) {
                        log.error("XM 导入歌手失败 userId: {}, singerType: {}", userId, singerType, e);
                    }
                }
            }

        }
        return importedCount;
    }

    @Override
    public List<Long> getRelatedEliminateSingerIds(List<Long> userIds, int appId, List<Long> ids) {

        if (CollUtil.isEmpty(userIds)){
            return CollUtil.newArrayList();
        }

        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserIds(userIds, appId, CollUtil.newArrayList(SingerStatusEnum.EFFECTIVE));
        if (CollUtil.isEmpty(singerInfoList)){
            return CollUtil.newArrayList();
        }
        return singerInfoList.stream().map(SingerInfoDTO::getId).collect(Collectors.toList());
    }

    @Override
    public ResponseGetAllSingerStatics fillSingerStatics(Integer appId, ResponseGetAllSingerStatics result) {
        // xm 不展示
        return result
                .setSeniorSingerCnt(0)
                .setAllIncomeSeniorSingerCnt(0)
                .setOriginalSeniorSinger(0)
                .setAllIncomeOriginalSeniorSingerCnt(0)
                ;
    }

    @Override
    public List<SingerInfoDTO> getWaitAutoEliminateSingerInfo(Integer appId, long userId) {
        return singerInfoManager.getSingerInfoByUserId(appId, userId, Lists.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING));
    }


    private boolean importSinger(Long userId, Integer singerType, BusinessEvnEnum evnEnum, Map<Integer, SongStyleBean> songStyleMap) {

        Optional<VocalSingerVerifyDto> bizSingerInfoOptional = getBizSingerInfo(userId);
        Optional<UserInFamilyBean> familyBeanOptional = Optional.ofNullable(familyManager.getUserInFamily(userId));
        SaveSingerInfoParamDTO param = new SaveSingerInfoParamDTO();
        param.setAppId(evnEnum.getAppId());
        param.setUserId(userId);
        param.setNjId(familyBeanOptional.map(UserInFamilyBean::getNjId).orElse(0L));
        param.setFamilyId(familyBeanOptional.map(UserInFamilyBean::getFamilyId).orElse(0L) );
        param.setSingerVerifyId(0L);
        param.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
        param.setSongStyle(bizSongStyleToWaveSongStyle(
                bizSingerInfoOptional.map(VocalSingerVerifyDto::getMusicType).orElse(null), songStyleMap)
        );
        param.setOriginalSinger(false);
        param.setSingerType(singerType);
        param.setOperator(SingerOperatorConstant.SYSTEM);
        param.setContactNumber("");
        return singerInfoManager.saveSingerInfo(param);
    }

    /**
     * 业务曲风转平台曲风
     */
    private String bizSongStyleToWaveSongStyle(Integer bizSongStyle, Map<Integer, SongStyleBean> songStyleMap) {


        if (bizSongStyle == null) {
            return "";
        }
        
        MusicTypeEnum musicTypeEnum = MusicTypeEnum.get(bizSongStyle);
        if (musicTypeEnum == null) {
            return "";
        }
        
        switch (musicTypeEnum) {
            // 写死，导入时暂时没有对应关系
            case TRADITION:
                return MapUtil.get(songStyleMap, 1, SongStyleBean.class, new SongStyleBean()).getName();
            case CANTON_POPULAR:
                return MapUtil.get(songStyleMap, 8, SongStyleBean.class, new SongStyleBean()).getName();
            case RHYTHM_BLUES:
                return MapUtil.get(songStyleMap, 2, SongStyleBean.class, new SongStyleBean()).getName();
            case POPULAR_FOLK:
                return MapUtil.get(songStyleMap, 3, SongStyleBean.class, new SongStyleBean()).getName();
            case POP_ROCK:
                return MapUtil.get(songStyleMap, 4, SongStyleBean.class, new SongStyleBean()).getName();
            case POPULAR_RAP:
                return MapUtil.get(songStyleMap, 5, SongStyleBean.class, new SongStyleBean()).getName();
            case ELECTRONIC_RAP:
                return MapUtil.get(songStyleMap, 6, SongStyleBean.class, new SongStyleBean()).getName();
            case MELODIC_RAP:
                return MapUtil.get(songStyleMap, 7, SongStyleBean.class, new SongStyleBean()).getName();
            case MANDARIN_POP:
                return MapUtil.get(songStyleMap, 9, SongStyleBean.class, new SongStyleBean()).getName();
            case INTERNET_POP:
                return MapUtil.get(songStyleMap, 10, SongStyleBean.class, new SongStyleBean()).getName();
            case LYRICAL_POP:
                return MapUtil.get(songStyleMap, 11, SongStyleBean.class, new SongStyleBean()).getName();
            default:
                return "";
        }
    }


    private Optional<VocalSingerVerifyDto> getBizSingerInfo(Long userId) {
        GetVocalSingerVerifyListReq req = new GetVocalSingerVerifyListReq();
        req.setUserId(userId);

        Result<GetVocalSingerVerifyListResp> result = vocalSingerVerifyService.list(req);
        if (RpcResult.isFail(result)){
            log.warn("xm getBizSingerInfo fail: uid:{}, rCode:{}", userId, result.rCode());
            return Optional.empty();
        }

        List<VocalSingerVerifyDto> list = Optional.ofNullable(result.target()).map(GetVocalSingerVerifyListResp::getSingerVerifyDtos).orElse(new ArrayList<>());
        if (CollUtil.isEmpty(list)){
            log.warn("xm getBizSingerInfo empty: uid:{}", userId);
            return Optional.empty();
        }

        return list.stream().findFirst();
    }
}
