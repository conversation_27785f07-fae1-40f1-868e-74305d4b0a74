package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerHallApplyWithStatsDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 点唱厅申请记录扩展Mapper
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface SingerHallApplyExtraMapper {

    /**
     * 分页查询点唱厅列表（带统计信息）
     */
    @Select("<script>" +
            "SELECT " +
            "  h.id, " +
            "  h.app_id AS appId, " +
            "  h.family_id AS familyId, " +
            "  h.nj_id AS njId, " +
            "  h.audit_status AS auditStatus, " +
            "  h.apply_time AS applyTime, " +
            "  h.deploy_env AS deployEnv, " +
            "  h.source, " +
            "  h.operator, " +
            "  h.deleted, " +
            "  h.create_time AS createTime, " +
            "  h.modify_time AS modifyTime, " +
            "  COALESCE(stats.singer_auth_cnt, 0) AS singerAuthCnt, " +
            "  COALESCE(stats.senior_singer_auth_cnt, 0) AS seniorSingerAuthCnt " +
            "FROM singer_sing_hall_apply_record h " +
            "LEFT JOIN ( " +
            "  SELECT " +
            "    nj_id, " +
            "    app_id, " +
            "    SUM(CASE WHEN effective_type = 1 THEN cnt ELSE 0 END) AS singer_auth_cnt, " +
            "    SUM(CASE WHEN effective_type = 2 THEN cnt ELSE 0 END) AS senior_singer_auth_cnt " +
            "  FROM ( " +
            "    SELECT " +
            "      nj_id, " +
            "      app_id, " +
            "      user_id, " +
            "      MAX(CASE WHEN singer_type IN (2,3) THEN 2 ELSE singer_type END) AS effective_type, " +
            "      COUNT(*) AS cnt " +
            "    FROM singer_info " +
            "    WHERE singer_status != 3 " +
            "    GROUP BY nj_id, app_id, user_id " +
            "  ) t " +
            "  GROUP BY nj_id, app_id " +
            ") stats ON h.nj_id = stats.nj_id AND h.app_id = stats.app_id " +
            "WHERE h.app_id = #{appId} " +
            "  AND h.deleted = 0 " +
            "  AND h.deploy_env = #{deployEnv} " +
            "<if test='auditStatus != null'>" +
            "  AND h.audit_status = #{auditStatus} " +
            "</if>" +
            "<if test='njId != null'>" +
            "  AND h.nj_id = #{njId} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "  AND h.create_time &gt;= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "  AND h.create_time &lt;= #{endTime} " +
            "</if>" +
            "ORDER BY " +
            "${orderBy} ${orderDirection}" +
            "</script>")
    PageList<SingerHallApplyWithStatsDTO> pageHallApplyListWithStats(
            @Param("appId") Integer appId,
            @Param("auditStatus") Integer auditStatus,
            @Param("njId") Long njId,
            @Param("startTime") java.util.Date startTime,
            @Param("endTime") java.util.Date endTime,
            @Param("deployEnv") String deployEnv,
            @Param("orderBy") String orderBy,
            @Param("orderDirection") String orderDirection,
            @Param(ParamContants.PAGE_NUMBER) Integer pageNumber,
            @Param(ParamContants.PAGE_SIZE) Integer pageSize
    );
}
