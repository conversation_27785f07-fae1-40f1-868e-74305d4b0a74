package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyApplySongInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyApplySongInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyApplySongInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerVerifyApplySongInfoDao {

    @Resource
    private SingerVerifyApplySongInfoMapper singerVerifyApplySongInfoMapper;

    /**
     * 获取认证申请的歌手曲风名
     *
     * @param applyIds 申请ID列表
     * @return 结果
     */
    public List<SingerVerifyApplySongInfo> getApplySongInfoListByApplyIds(List<Long> applyIds) {
        SingerVerifyApplySongInfoExample example = new SingerVerifyApplySongInfoExample();
        example.createCriteria().andApplyIdIn(applyIds);
        return singerVerifyApplySongInfoMapper.selectByExample(example);
    }

    /**
     * 认证申请的曲风名
     *
     * @param applyIds 申请ID列表
     * @return 结果，key为applyId，value为曲风名
     */
    public Map<Long, String> getApplySongInfoMap(List<Long> applyIds) {
        List<SingerVerifyApplySongInfo> res = getApplySongInfoListByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(res)) {
            return new HashMap<>(4);
        }
        //先按照applyId对res进行分组
        Map<Long, List<SingerVerifyApplySongInfo>> groupByApplyId = res.stream().collect(Collectors.groupingBy(SingerVerifyApplySongInfo::getApplyId));
        //将分组后的结果列表中的SingerVerifyApplySongInfo.songStyle进行拼接，多个用逗号分隔
        return groupByApplyId.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> JoinerUtils.join(entry.getValue().stream().map(SingerVerifyApplySongInfo::getSongStyle)
                                .collect(Collectors.toList()))));
    }

}
