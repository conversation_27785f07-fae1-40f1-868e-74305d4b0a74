package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerBlackList;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerBlackListConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerBlackListDao;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerBlackListDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInBlackResultDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerBlackListManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerBlackListManagerImpl implements SingerBlackListManager {

    @Autowired
    private SingerBlackListDao singerBlackListDao;

    @Autowired
    private UserVerifyManager userVerifyManager;

    @Autowired
    private IdManager idManager;

    @Override
    public Result<SingerInBlackResultDTO> isInBlackList(Integer appId, Long userId, String idCardNumber) {
        // 先确认当前用户是不是被拉黑了
        Boolean inBlackList = isInBlackList(appId, userId);
        // 如果当前用户被拉黑了，直接返回true
        if (inBlackList) {
            return RpcResult.success(new SingerInBlackResultDTO()
                    .setInBlackList(true));
        }

        // 先确认当前用户是不是被拉黑了
        List<SingerBlackList> singerBlackListByIdCard = singerBlackListDao.getSingerBlackListByIdCard(idCardNumber, appId);
        // 和当前用户同个证件实名的账号被拉黑了，也直接返回true
        return RpcResult.success(new SingerInBlackResultDTO()
                .setInBlackList(CollectionUtils.isNotEmpty(singerBlackListByIdCard)));
    }

    @Override
    public Boolean isInBlackList(Integer appId, Long userId) {
        // 先确认当前用户是不是被拉黑了
        SingerBlackList singerBlackList = singerBlackListDao.getSingerBlackListByUserId(appId, userId);
        return singerBlackList != null;
    }


    @Override
    public boolean addBlackList(Integer appId, Long userId, String idCardNumber) {
        SingerBlackList singerBlackList = SingerBlackList.builder()
                .appId(appId).userId(userId)
                .idCardNumber(idCardNumber)
                .deployEnv(ConfigUtils.getEnvRequired().name()).build();
        return singerBlackListDao.saveSingerBlackList(singerBlackList);
    }

    @Override
    public boolean cancelBlackList(Integer appId, String idCardNumber) {
        return singerBlackListDao.cancelSingerBlackList(appId, idCardNumber);
    }

    @Override
    public Map<String, SingerBlackListDTO> searchBlackListByCertNo(Integer appId, List<String> idCardNumberList) {
        if (CollectionUtils.isEmpty(idCardNumberList)) {
            return Collections.emptyMap();
        }

        List<SingerBlackList> singerBlackListRes = singerBlackListDao.getSingerBlackListByCertNo(appId, idCardNumberList);
        List<SingerBlackListDTO> beanListtoDTOList = SingerBlackListConvert.I.beanListtoDTOList(singerBlackListRes);
        // 判空
        if (CollectionUtils.isEmpty(beanListtoDTOList)) {
            return Collections.emptyMap();
        }
        return beanListtoDTOList.stream().collect(Collectors.toMap(SingerBlackListDTO::getIdCardNumber, Function.identity()));
    }




    @Override
    public boolean batchAddBlackList(Integer appId, Map<Long, String> userIdToIdCardMap) {
        //批量构建拉黑信息
        List<SingerBlackList> singerBlackListList = new ArrayList<>();
        for (Map.Entry<Long, String> entry : userIdToIdCardMap.entrySet()) {
            SingerBlackList singerBlackList = SingerBlackList.builder()
                    .id(idManager.genId())
                    .appId(appId).userId(entry.getKey())
                    .idCardNumber(entry.getValue())
                    .createTime(new Date())
                    .deployEnv(ConfigUtils.getEnvRequired().name()).build();
            singerBlackListList.add(singerBlackList);
        }
        //批量保存
        return singerBlackListDao.batchSaveSingerBlackList(singerBlackListList);
    }


    @Override
    public List<Long> batchCancelBlackList(Integer appId, Map<Long, String> idCardNumberMap) {
        List<Long> losingUserIds = new ArrayList<>();
        for (Map.Entry<Long, String> entry : idCardNumberMap.entrySet()) {
            //批量取消拉黑
            boolean res = singerBlackListDao.cancelSingerBlackList(appId, entry.getValue());
            if (!res) {
                losingUserIds.add(entry.getKey());
            }
        }
        return losingUserIds;
    }

}
