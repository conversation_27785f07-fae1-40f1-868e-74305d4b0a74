package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataRoomDay;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerRoomDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 歌手厅日数据转换
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SingerDataRoomDayConvert {

    SingerDataRoomDayConvert I = Mappers.getMapper(SingerDataRoomDayConvert.class);

    /**
     * 实体转DTO
     */
    @Mapping(source = "lastWeekIncome", target = "income")
    SingerRoomDetailDTO convertToDTO(SingerDataRoomDay entity);

    /**
     * 实体列表转DTO列表
     */
    List<SingerRoomDetailDTO> convertToDTOList(List<SingerDataRoomDay> entityList);
} 