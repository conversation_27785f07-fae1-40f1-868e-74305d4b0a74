package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 导入歌手库
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImportSingerJob implements JobHandler {

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public void execute(JobExecuteContext context) throws Exception {

        Map<Integer, List<Long>> ppImportSingerMap = singerAnchorConfig.getPp().getImportSingerMap();
        Map<Integer, List<Long>> xmImportSingerMap = singerAnchorConfig.getXm().getImportSingerMap();
        Map<Integer, List<Long>> hyImportSingerMap = singerAnchorConfig.getHy().getImportSingerMap();

        
        if (CollUtil.isNotEmpty(ppImportSingerMap)){
            try {
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
                ISingerProcessor processor = processorFactory.getProcessor(ISingerProcessor.class);
                int count = processor.importSinger(ppImportSingerMap);
                log.info("PP 导入歌手库完成，本次导入数量：{}", count);
            }catch (Exception e){
                log.error("PP 导入歌手库失败", e);
            }finally {
                ContextUtils.clearContext();
            }
        }

        if (CollUtil.isNotEmpty(xmImportSingerMap)){
            try {
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
                ISingerProcessor processor = processorFactory.getProcessor(ISingerProcessor.class);
                int count = processor.importSinger(xmImportSingerMap);
                log.info("XM 导入歌手库完成，本次导入数量：{}", count);
            }catch (Exception e){
                log.error("XM 导入歌手库失败", e);
            }finally {
                ContextUtils.clearContext();
            }

        }

        if (CollUtil.isNotEmpty(hyImportSingerMap)){
            try {
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
                ISingerProcessor processor = processorFactory.getProcessor(ISingerProcessor.class);
                int count = processor.importSinger(hyImportSingerMap);
                log.info("HY 导入歌手库完成，本次导入数量：{}", count);
            }catch (Exception e){
                log.error("HY 导入歌手库失败", e);
            }finally {
                ContextUtils.clearContext();
            }
        }
    }

}
