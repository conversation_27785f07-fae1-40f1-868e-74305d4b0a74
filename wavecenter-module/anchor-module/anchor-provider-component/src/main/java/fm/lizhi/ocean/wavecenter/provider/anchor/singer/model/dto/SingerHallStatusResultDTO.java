package fm.lizhi.ocean.wavecenter.provider.anchor.singer.model.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SingerHallStatusResultDTO {

    /**
     * 是否需要校验点唱厅状态
     */
    private boolean needCheck;

    /**
     * 点唱厅状态
     */
    Map<Long, Integer> statusMap;

    /**
     * 构造方法
     *
     * @param needCheck 是否需要校验点唱厅状态
     * @param statusMap 点唱厅状态
     * @return 点唱厅状态结果
     */
    public static SingerHallStatusResultDTO of(boolean needCheck, Map<Long, Integer> statusMap) {
        SingerHallStatusResultDTO result = new SingerHallStatusResultDTO();
        result.setNeedCheck(needCheck);
        result.setStatusMap(statusMap);
        return result;
    }
}
