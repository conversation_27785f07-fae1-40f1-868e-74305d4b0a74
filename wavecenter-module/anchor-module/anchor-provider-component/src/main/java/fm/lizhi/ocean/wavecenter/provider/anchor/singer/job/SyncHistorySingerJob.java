package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager.SyncHistorySingerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncHistorySingerJob implements JobHandler {

    @Autowired
    private SyncHistorySingerManager syncHistorySingerManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        syncHistorySingerManager.syncHistorySinger(context.getParam());
    }


}