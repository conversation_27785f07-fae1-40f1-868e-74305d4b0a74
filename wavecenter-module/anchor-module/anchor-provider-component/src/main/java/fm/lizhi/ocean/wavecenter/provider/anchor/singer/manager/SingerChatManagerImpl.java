package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerAuditChatConfigDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerAuditChatConfigMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerChatProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 歌手私信管理
 */
@Slf4j
@Component
public class SingerChatManagerImpl implements SingerChatManager {

    @Autowired
    private ChatManager chatManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private SingerAuditChatConfigDao singerAuditChatConfigDao;

    @Autowired
    private SingerAuditChatConfigMapper singerAuditChatConfigMapper;

    public static final ExecutorService executorService = ThreadUtils.getTtlExecutors("singer-chat-pool", 50, 50);

    @Override
    public void sendAuditResultChat(Integer appId, Long singerId, Integer singerType, String songStyle,SingerChatSceneEnum scene) {
        ISingerChatProcessor processor = processorFactory.getProcessor(ISingerChatProcessor.class);
        if (!processor.needSendHallAuditingChat(scene)) {
            return;
        }
        try {
            List<SingerChatSceneDTO> configs = singerAuditChatConfigDao.getSingerAuditChatConfig(appId, singerType, scene.getSceneCode());
            //如果文案为空，则不发送私信
            if (CollectionUtils.isEmpty(configs)) {
                log.error("歌手私信管理：场景编码[{}]未配置文案", scene.getSceneCode());
                return;
            }
            SingerChatSceneDTO config = configs.get(0);
            String content = config.getContent();
            if (StringUtils.isEmpty(content)){
                log.warn("歌手私信管理：场景编码[{}]未配置文案", scene.getSceneCode());
                return;
            }

            if (StringUtils.isNotBlank(content)) {
                content = content.replace("${songStyle}", songStyle);
            }
            if (StringUtils.isEmpty(config.getActionUrl())) {
                chatManager.sendChatAsync(singerId, content);
            } else {
                //发送私信
                chatManager.sendChatAsyncWithSkipWeb(singerId, content, config.getActionUrl());
            }
        } catch (Exception e) {
            log.error("歌手私信管理：发送私信异常，appId={}, singerId={}, scene={}", appId, singerId, scene, e);
        }
    }

    @Override
    public void batchSendAuditResultChat(Integer appId, List<SingerInfoDTO> singerInfoList, SingerChatSceneEnum scene) {
        singerInfoList.forEach(singer -> {
            sendAuditResultChat(appId, singer.getUserId(), singer.getSingerType(), singer.getSongStyle(), scene);
        });
    }

    @Override
    public void batchSendAuditResultChatAsync(Integer appId, List<SingerInfoDTO> singerInfoList, SingerChatSceneEnum scene) {
        executorService.submit(() -> {
            batchSendAuditResultChat(appId, singerInfoList, scene);
        });
    }

    @Override
    public void addAuditChatConfig(SingerChatSceneDTO config) {
        try {
            singerAuditChatConfigDao.insert(config);
        } catch (Exception e) {
            log.error("歌手私信管理：新增配置异常，config={}", config, e);
            throw new RuntimeException("新增配置失败");
        }
    }

    @Override
    public void deleteAuditChatConfig(Integer appId, String sceneCode) {
        try {
            singerAuditChatConfigDao.deleteByAppIdAndSceneCode(appId, sceneCode);
        } catch (Exception e) {
            log.error("歌手私信管理：删除配置异常，appId={}, sceneCode={}", appId, sceneCode, e);
            throw new RuntimeException("删除配置失败");
        }
    }

    @Override
    public void updateAuditChatConfig(SingerChatSceneDTO config) {
        try {
            singerAuditChatConfigDao.updateByPrimaryKey(config);
        } catch (Exception e) {
            log.error("歌手私信管理：修改配置异常，config={}", config, e);
            throw new RuntimeException("修改配置失败");
        }
    }

    @Override
    public void updateAuditChatConfigByAppIdAndSceneCode(Integer appId, Integer singerType, String sceneCode, String content, String actionUrl) {
        try {
            singerAuditChatConfigDao.updateByAppIdAndSceneCode(appId, singerType, sceneCode, content, actionUrl);
        } catch (Exception e) {
            log.error("歌手私信管理：更新配置异常，appId={}, singerType={}, sceneCode={}", appId, singerType, sceneCode, e);
            throw new RuntimeException("更新配置失败");
        }
    }

    @Override
    public List<SingerChatSceneDTO> getAuditChatConfig(Integer appId, Integer singerType, String sceneCode) {
        try {
            return singerAuditChatConfigDao.getSingerAuditChatConfig(appId, singerType, sceneCode);
        } catch (Exception e) {
            log.error("歌手私信管理：查询配置异常，appId={}, singerType={}, sceneCode={}", appId, singerType, sceneCode, e);
            throw new RuntimeException("查询配置失败");
        }
    }
}