package fm.lizhi.ocean.wavecenter.provider.anchor.singer.remote.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.remote.IFamilySingHallApplyRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyFamilySingHallApplyRemote implements IFamilySingHallApplyRemote {


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean isInApply(Long njId) {
        return false;
    }
}
