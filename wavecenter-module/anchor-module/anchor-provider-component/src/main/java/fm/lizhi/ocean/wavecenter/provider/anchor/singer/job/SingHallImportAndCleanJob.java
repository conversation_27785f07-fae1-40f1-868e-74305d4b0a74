package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplySourceEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerSingHallApplyRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRoomFamilyWeekMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.CommonSingerConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 点唱厅自动导入/淘汰
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingHallImportAndCleanJob implements JobHandler {


    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private WcDataRoomFamilyWeekMapper wcDataRoomFamilyWeekMapper;

    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Autowired
    private SingerSingHallApplyRecordMapper singerSingHallApplyRecordMapper;


    @Override
    public void execute(JobExecuteContext context) throws Exception {

        for (BusinessEvnEnum evnEnum : BusinessEvnEnum.values()) {
            if (evnEnum.getOnline() != 1) {
                continue;
            }
            AtomicInteger importCount = new AtomicInteger(0);
            AtomicInteger cleanCount = new AtomicInteger(0);

            try {
                log.info("[SingHallImportAndCleanJob] start, appId:{}", evnEnum.getAppId());
                ContextUtils.setBusinessEvnEnum(evnEnum);
                if (singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
                    //弱关联，不自动导入，较长时间需求没有变动，代码可以删除
                    continue;
                }
                try {
                    // 导入
                    importCount.addAndGet(importHall(evnEnum));

                    // 清理
                    cleanCount.addAndGet(cleanHall(evnEnum));

                } catch (Exception e) {
                    log.error("[SingHallImportAndCleanJob] 点唱厅导入/淘汰失败. appId:{}", evnEnum.getAppId(), e);
                }

            } finally {
                // 无论是否发生异常，最终执行清理
                log.info("[SingHallImportAndCleanJob] end. appId:{}, importCount:{}, cleanCount:{}",
                        evnEnum.getAppId(), importCount.get(), cleanCount.get());
                ContextUtils.clearContext();
            }
        }
    }

    /**
     * 清理
     */
    private int cleanHall(BusinessEvnEnum evnEnum) {

        CommonSingerConfig config = singerAnchorConfig.getBizConfig(evnEnum.getAppId());
        if (!config.isEnableCleanSingHall()) {
            log.info("[SingHallImportAndCleanJob] 点唱厅淘汰功能未开启. appId:{}", evnEnum.getAppId());
            return 0;
        }
        Date startDay = MyDateUtil.getLastWeekStartDay();
        Date endDay = MyDateUtil.getLastWeekEndDay();
        Long allIncome = config.getImportSingHallGtAllIncome();
        AtomicInteger count = new AtomicInteger();
        int pageNo = 1;
        int pageSize = 30;
        PageList<WcDataRoomFamilyWeek> list = new PageList<>();

        do {
            try {
                list = pageRoomWeekDataLtAllIncome(evnEnum.getAppId(), startDay, endDay, allIncome, pageNo, pageSize);
                if (list.isEmpty()) {
                    break;
                }
                List<Long> njIds = list.stream().map(WcDataRoomFamilyWeek::getRoomId).collect(Collectors.toList());
                Map<Long, Integer> hallStatusMap = singerHallApplyManager.batchGetSingerHallStatusMap(evnEnum.getAppId(), njIds);

                // 过滤出需要淘汰的厅
                List<Long> needCleanNjIds = hallStatusMap.keySet().stream()
                        .filter(njId -> hallStatusMap.get(njId) == SingerHallApplyStatusEnum.APPLYING.getStatus())
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(needCleanNjIds)) {
                    continue;
                }

                // 淘汰点唱厅
                List<SingerSingHallApplyRecord> recordList = getSingerHallApplyRecordByNjIds(needCleanNjIds, evnEnum.getAppId());
                recordList.forEach(record -> {
                    Optional<SingerSingHallApplyRecordBean> success = singerHallApplyManager.rejectHallApply(new RequestOperateHallApply()
                            .setAppId(evnEnum.appId())
                            .setOperator(SingerOperatorConstant.SYSTEM)
                            .setId(record.getId())
                            .setStatus(SingerHallApplyStatusEnum.REJECTED));

                    success.ifPresent(bean -> count.incrementAndGet());
                    log.info("[SingHallImportAndCleanJob] 点唱厅淘汰. appId:{}, hallId:{}, success:{}", evnEnum.getAppId(), record.getNjId(), success.isPresent());
                });

                return count.get();
            } catch (Exception e) {
                log.error("[SingHallImportAndCleanJob] 点唱厅淘汰处理异常. appId:{}, pageNo:{}", evnEnum.getAppId(), pageNo, e);
            } finally {
                pageNo++;
            }
        } while (list.isHasNextPage());

        return count.get();
    }

    /**
     * 导入
     */
    private int importHall(BusinessEvnEnum evnEnum) {
        CommonSingerConfig config = singerAnchorConfig.getBizConfig(evnEnum.getAppId());
        if (!config.isEnableImportSingHall()) {
            log.info("[SingHallImportAndCleanJob] 点唱厅导入功能未开启. appId:{}", evnEnum.getAppId());
            return 0;
        }

        int pageNo = 1;
        int pageSize = 30;
        int count = 0;
        Date startDay = MyDateUtil.getLastWeekStartDay();
        Date endDay = MyDateUtil.getLastWeekEndDay();
        Long allIncome = config.getImportSingHallGtAllIncome();
        PageList<WcDataRoomFamilyWeek> list = new PageList<>();

        do {
            try {
                list = pageRoomWeekDataGtAllIncome(evnEnum.getAppId(), startDay, endDay, allIncome, pageNo, pageSize);
                if (list.isEmpty()) {
                    break;
                }

                List<Long> njIds = list.stream().map(WcDataRoomFamilyWeek::getRoomId).collect(Collectors.toList());
                List<SingerSingHallApplyRecordBean> existSingHallList = singerHallApplyManager.getSingerHallApplyRecordByNjIdsAndAppId(njIds, evnEnum.getAppId());

                List<Long> existNjIds = CollUtil.isEmpty(existSingHallList) ?
                        Collections.emptyList() :
                        existSingHallList.stream()
                                .map(SingerSingHallApplyRecordBean::getNjId)
                                .collect(Collectors.toList());

                // 过滤出需要导入的厅
                List<Long> needImportList = list.stream()
                        .map(WcDataRoomFamilyWeek::getRoomId)
                        .filter(roomId -> !existNjIds.contains(roomId))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(needImportList)) {
                    // 批量导入点唱厅记录
                    boolean success = singerHallApplyManager.importHallApply(new RequestImportHallApply()
                            .setAppId(evnEnum.appId())
                            .setOperator(SingerOperatorConstant.SYSTEM)
                            .setNjIds(needImportList)
                            .setSource(SingerHallApplySourceEnum.JOB_TASK)
                            .setStatus(SingerHallApplyStatusEnum.APPLYING)
                    );

                    count += success ? needImportList.size() : 0;
                    log.info("[SingHallImportAndCleanJob] 批量导入点唱厅成功. appId:{}, 批量大小:{}, 成功数量:{}", evnEnum.getAppId(), needImportList.size(), count);
                }

            } catch (Exception e) {
                log.error("[SingHallImportAndCleanJob] 点唱厅导入处理异常. appId:{}, pageNo:{}", evnEnum.getAppId(), pageNo, e);
            } finally {
                pageNo++;
            }

        } while (list.isHasNextPage());

        log.info("[SingHallImportAndCleanJob] 点唱厅导入完成. appId:{}, 导入数量:{}", evnEnum.getAppId(), count);
        return count;
    }

    /**
     * 获取周流水大于指定金额的厅数据
     */
    private PageList<WcDataRoomFamilyWeek> pageRoomWeekDataGtAllIncome(int appId, Date startWeek, Date endWeek, long gtIncome, int pageNo, int pageSize) {
        WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStartWeekDateEqualTo(startWeek)
                .andEndWeekDateEqualTo(endWeek)
                .andAllIncomeGreaterThan(new BigDecimal(gtIncome))
        ;
        return wcDataRoomFamilyWeekMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 获取周流水小于指定金额的厅数据
     */
    private PageList<WcDataRoomFamilyWeek> pageRoomWeekDataLtAllIncome(int appId, Date startWeek, Date endWeek, long gtIncome, int pageNo, int pageSize) {
        WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStartWeekDateEqualTo(startWeek)
                .andEndWeekDateEqualTo(endWeek)
                .andAllIncomeLessThan(new BigDecimal(gtIncome))
        ;
        return wcDataRoomFamilyWeekMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 点唱厅审核中的申请记录
     */
    private List<SingerSingHallApplyRecord> getSingerHallApplyRecordByNjIds(List<Long> njIds, int appId) {
        if (CollUtil.isEmpty(njIds)) {
            return Collections.emptyList();
        }

        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        example.createCriteria()
                .andNjIdIn(njIds)
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(false)
                .andAuditStatusEqualTo(SingerHallApplyStatusEnum.APPLYING.getStatus())
                .andSourceEqualTo(SingerHallApplySourceEnum.JOB_TASK.getSource())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        return singerSingHallApplyRecordMapper.selectByExample(example);
    }


}
