package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext;

import java.util.List;
import java.util.Date;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import fm.lizhi.common.datastore.core.annotation.DataStore;


@DataStore(namespace = "mysql_ocean_wavecenter")
public interface SingerVerifyRecordExtraMapper {

    /**
     * 分页查询歌手认证记录（包含黑名单）
     *
     * @param pageNo         页码
     * @param appId          应用ID
     * @param userId         用户ID
     * @param njId           厅ID
     * @param singerType     歌手类型
     * @param minApplyTime   最小申请时间
     * @param maxApplyTime   最大申请时间
     * @param songStyle      歌曲风格
     * @param auditStatus    审核状态
     * @param originalSinger 是否原创歌手
     * @param pageSize       每页大小
     * @param orderMetrics
     * @param value
     * @return 分页结果
     */
    @Select({
            "<script>",
            "SELECT svr.* FROM singer_black_list sbl",
            "INNER JOIN singer_verify_record svr ON sbl.app_id = svr.app_id AND sbl.user_id = svr.user_id",
            "WHERE sbl.deploy_env = #{deployEnv}",
            "AND svr.deploy_env = #{deployEnv}",
            "<if test='appId != null'>",
            "   AND sbl.app_id = #{appId}",
            "</if>",
            "<if test='userId != null'>",
            "   AND sbl.user_id = #{userId}",
            "</if>",
            "<if test='njId != null'>",
            "   AND svr.nj_id = #{njId}",
            "</if>",
            "<if test='singerType != null'>",
            "   AND svr.singer_type = #{singerType}",
            "</if>",
            "<if test='minApplyTime != null'>",
            "   AND svr.create_time &gt;= #{minApplyTime}",
            "</if>",
            "<if test='maxApplyTime != null'>",
            "   AND svr.create_time &lt;= #{maxApplyTime}",
            "</if>",
            "<if test='songStyle != null and songStyle.size() > 0'>",
            "   AND (",
            "       EXISTS (",
            "           SELECT 1 FROM singer_verify_apply_song_info sasi",
            "           WHERE sasi.apply_id = svr.id",
            "           AND sasi.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "       OR (",
            "           svr.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "   )",
            "</if>",
            "<if test='auditStatus != null and auditStatus.size() > 0'>",
            "   AND svr.audit_status IN",
            "   <foreach collection='auditStatus' item='status' open='(' separator=',' close=')'>",
            "       #{status}",
            "   </foreach>",
            "</if>",
            "<if test='originalSinger != null'>",
            "   AND svr.original_singer = #{originalSinger}",
            "</if>",
            "ORDER BY svr.${orderMetrics} ${orderType}",
            "LIMIT #{offset}, #{pageSize}",
            "</script>"
    })
    List<SingerVerifyRecord> pageQuerySingerVerifyRecordWithBlackList(
            @Param("appId") Integer appId,
            @Param("userId") Long userId,
            @Param("njId") Long njId,
            @Param("singerType") Integer singerType,
            @Param("minApplyTime") Date minApplyTime,
            @Param("maxApplyTime") Date maxApplyTime,
            @Param("songStyle") List<String> songStyle,
            @Param("auditStatus") List<Integer> auditStatus,
            @Param("originalSinger") Boolean originalSinger,
            @Param("deployEnv") String deployEnv,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize,
            @Param("orderMetrics") String orderMetrics,
            @Param("orderType") String orderType);

    /**
     * 分页查询歌手认证记录（不包含黑名单）
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param njId           厅ID
     * @param singerType     歌手类型
     * @param minApplyTime   最小申请时间
     * @param maxApplyTime   最大申请时间
     * @param songStyle      歌曲风格
     * @param auditStatus    审核状态
     * @param originalSinger 是否原创歌手
     * @param pageSize       每页大小
     * @param orderMetrics
     * @return 分页结果
     */
    @Select({
            "<script>",
            "SELECT svr.* FROM singer_verify_record svr",
            "WHERE svr.deploy_env = #{deployEnv}",
            "<if test='appId != null'>",
            "   AND svr.app_id = #{appId}",
            "</if>",
            "<if test='userId != null'>",
            "   AND svr.user_id = #{userId}",
            "</if>",
            "<if test='njId != null'>",
            "   AND svr.nj_id = #{njId}",
            "</if>",
            "<if test='singerType != null'>",
            "   AND svr.singer_type = #{singerType}",
            "</if>",
            "<if test='minApplyTime != null'>",
            "   AND svr.create_time &gt;= #{minApplyTime}",
            "</if>",
            "<if test='maxApplyTime != null'>",
            "   AND svr.create_time &lt;= #{maxApplyTime}",
            "</if>",
            "<if test='songStyle != null and songStyle.size() > 0'>",
            "   AND (",
            "       EXISTS (",
            "           SELECT 1 FROM singer_verify_apply_song_info sasi",
            "           WHERE sasi.apply_id = svr.id",
            "           AND sasi.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "       OR (",
            "           svr.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "   )",
            "</if>",
            "<if test='auditStatus != null and auditStatus.size() > 0'>",
            "   AND svr.audit_status IN",
            "   <foreach collection='auditStatus' item='status' open='(' separator=',' close=')'>",
            "       #{status}",
            "   </foreach>",
            "</if>",
            "<if test='originalSinger != null'>",
            "   AND svr.original_singer = #{originalSinger}",
            "</if>",
            "ORDER BY svr.${orderMetrics} ${orderType}",
            "LIMIT #{offset}, #{pageSize}",
            "</script>"
    })
    List<SingerVerifyRecord> pageQuerySingerVerifyRecord(
            @Param("appId") Integer appId,
            @Param("userId") Long userId,
            @Param("njId") Long njId,
            @Param("singerType") Integer singerType,
            @Param("minApplyTime") Date minApplyTime,
            @Param("maxApplyTime") Date maxApplyTime,
            @Param("songStyle") List<String> songStyle,
            @Param("auditStatus") List<Integer> auditStatus,
            @Param("originalSinger") Boolean originalSinger,
            @Param("deployEnv") String deployEnv,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize,
            @Param("orderMetrics") String orderMetrics,
            @Param("orderType") String orderType);

    /**
     * 查询歌手认证记录总数（包含黑名单）
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) FROM singer_black_list sbl",
            "INNER JOIN singer_verify_record svr ON sbl.app_id = svr.app_id AND sbl.user_id = svr.user_id",
            "WHERE sbl.deploy_env = #{deployEnv}",
            "AND svr.deploy_env = #{deployEnv}",
            "<if test='appId != null'>",
            "   AND sbl.app_id = #{appId}",
            "</if>",
            "<if test='userId != null'>",
            "   AND sbl.user_id = #{userId}",
            "</if>",
            "<if test='njId != null'>",
            "   AND svr.nj_id = #{njId}",
            "</if>",
            "<if test='singerType != null'>",
            "   AND svr.singer_type = #{singerType}",
            "</if>",
            "<if test='minApplyTime != null'>",
            "   AND svr.create_time &gt;= #{minApplyTime}",
            "</if>",
            "<if test='maxApplyTime != null'>",
            "   AND svr.create_time &lt;= #{maxApplyTime}",
            "</if>",
            "<if test='songStyle != null and songStyle.size() > 0'>",
            "   AND (",
            "       EXISTS (",
            "           SELECT 1 FROM singer_verify_apply_song_info sasi",
            "           WHERE sasi.apply_id = svr.id",
            "           AND sasi.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "       OR (",
            "           svr.song_style IN",
            "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
            "               #{style}",
            "           </foreach>",
            "       )",
            "   )",
            "</if>",
            "<if test='auditStatus != null and auditStatus.size() > 0'>",
            "   AND svr.audit_status IN",
            "   <foreach collection='auditStatus' item='status' open='(' separator=',' close=')'>",
            "       #{status}",
            "   </foreach>",
            "</if>",
            "<if test='originalSinger != null'>",
            "   AND svr.original_singer = #{originalSinger}",
            "</if>",
            "</script>"
    })
    Long countSingerVerifyRecordWithBlackList(
            @Param("appId") Integer appId,
            @Param("userId") Long userId,
            @Param("njId") Long njId,
            @Param("singerType") Integer singerType,
            @Param("minApplyTime") Date minApplyTime,
            @Param("maxApplyTime") Date maxApplyTime,
            @Param("songStyle") List<String> songStyle,
            @Param("auditStatus") List<Integer> auditStatus,
            @Param("originalSinger") Boolean originalSinger,
            @Param("deployEnv") String deployEnv);

    /**
     * 查询歌手认证记录总数（不含黑名单）
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM singer_verify_record svr",
        "WHERE svr.deploy_env = #{deployEnv}",
        "<if test='appId != null'>",
        "   AND svr.app_id = #{appId}",
        "</if>",
        "<if test='userId != null'>",
        "   AND svr.user_id = #{userId}",
        "</if>",
        "<if test='njId != null'>",
        "   AND svr.nj_id = #{njId}",
        "</if>",
        "<if test='singerType != null'>",
        "   AND svr.singer_type = #{singerType}",
        "</if>",
        "<if test='minApplyTime != null'>",
        "   AND svr.create_time &gt;= #{minApplyTime}",
        "</if>",
        "<if test='maxApplyTime != null'>",
        "   AND svr.create_time &lt;= #{maxApplyTime}",
        "</if>",
        "<if test='songStyle != null and songStyle.size() > 0'>",
        "   AND (",
        "       EXISTS (",
        "           SELECT 1 FROM singer_verify_apply_song_info sasi",
        "           WHERE sasi.apply_id = svr.id",
        "           AND sasi.song_style IN",
        "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
        "               #{style}",
        "           </foreach>",
        "       )",
        "       OR (",
        "           svr.song_style IN",
        "           <foreach collection='songStyle' item='style' open='(' separator=',' close=')'>",
        "               #{style}",
        "           </foreach>",
        "       )",
        "   )",
        "</if>",
        "<if test='auditStatus != null and auditStatus.size() > 0'>",
        "   AND svr.audit_status IN",
        "   <foreach collection='auditStatus' item='status' open='(' separator=',' close=')'>",
        "       #{status}",
        "   </foreach>",
        "</if>",
        "<if test='originalSinger != null'>",
        "   AND svr.original_singer = #{originalSinger}",
        "</if>",
        "</script>"
    })
    Long countSingerVerifyRecord(
            @Param("appId") Integer appId,
            @Param("userId") Long userId,
            @Param("njId") Long njId,
            @Param("singerType") Integer singerType,
            @Param("minApplyTime") Date minApplyTime,
            @Param("maxApplyTime") Date maxApplyTime,
            @Param("songStyle") List<String> songStyle,
            @Param("auditStatus") List<Integer> auditStatus,
            @Param("originalSinger") Boolean originalSinger,
            @Param("deployEnv") String deployEnv);
}
