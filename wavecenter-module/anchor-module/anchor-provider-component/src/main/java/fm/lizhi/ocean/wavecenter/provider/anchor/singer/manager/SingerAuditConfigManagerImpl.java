package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerAuditConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerAuditConfigConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerAuditConfigDao;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerAuditConfigManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SingerAuditConfigManagerImpl implements SingerAuditConfigManager {

    @Autowired
    private SingerRedisManager singerRedisManager;

    @Autowired
    private SingerAuditConfigDao singerAuditConfigDao;
    @Autowired
    private SingerAnchorConfig singerAnchorConfig;


    @Override
    public List<SingerAuditConfigDTO> getSingerAuditConfig(Integer appId) {
        List<SingerAuditConfig> configs = singerAuditConfigDao.getSingerAuditConfig(appId);
        List<SingerAuditConfigDTO> dtos = SingerAuditConfigConvert.I.configsToDTOs(configs);
        //弱关联去掉点唱厅预审校验
        if (singerAnchorConfig.getBizConfig(appId).isLessRelevanceSwitch()) {
            dtos.removeIf(config -> SingerAuditConfigCodeEnum.SIGNED_HALL_STATUS.getCode().equals(config.getConfigCode()));
        }
        return dtos;
    }

    @Override
    public Result<Void> updateSingerAuditConfig(RequestUpdateSingerAuditConfig request) {
        List<SingerAuditConfig> singerAuditConfigs = SingerAuditConfigConvert.I.requestToConfig(request.getConfigList());
        for (SingerAuditConfig singerAuditConfig : singerAuditConfigs) {
            singerAuditConfig.setOperator(request.getOperator());
            boolean res = singerAuditConfigDao.updateSingerAuditConfig(singerAuditConfig);
            if (!res) {
                return RpcResult.fail(UPDATE_SINGER_AUDIT_CONFIG_FAIL);
            }
        }
        return RpcResult.success();
    }

    @Override
    public Result<List<SingerAuditConfigDTO>> getEnableSingerAuditConfig(Integer appId) {
        List<SingerAuditConfig> configs = singerAuditConfigDao.getEnableSingerAuditConfig(appId);
        return RpcResult.success(SingerAuditConfigConvert.I.configsToDTOs(configs));
    }

    @Override
    public List<SingerAuditConfigDTO> getSingerAuditConfigByScene(Integer appId, Integer scene) {
        List<SingerAuditConfig> configs = singerAuditConfigDao.getSingerAuditConfigByScene(appId, scene);
        return SingerAuditConfigConvert.I.configsToDTOs(configs);
    }

    @Override
    public void saveApplyMenuConfig(RequestSaveApplyMenuConfig request) {
        singerRedisManager.saveApplyMenuConfig(SingerAuditConfigConvert.I.convertSingerMenuConfigDTO(request));
    }

    @Override
    public SingerMenuConfigDTO getApplyMenuConfig(int appId, Integer singerType) {
        return singerRedisManager.getApplyMenuConfig(appId, singerType);
    }

    @Override
    public boolean checkMenuConfig(int appId, Integer singerType) {
        SingerMenuConfigDTO config = getApplyMenuConfig(appId, singerType);
        SingerTypeEnum type = SingerTypeEnum.getByType(singerType);
        return checkMenuConfig(config, type);
    }

    @Override
    public boolean checkMenuConfig(SingerMenuConfigDTO config, SingerTypeEnum type) {
        if (config == null || !config.getEnabled()) {
            return false;
        }

        Date now = new Date();
        Date start = new Date(config.getStartTime());
        Date end = new Date(config.getEndTime());
        boolean inTime = DateUtil.isIn(now, start, end);

        if (inTime) {
            log.info("{} singer config is active, config:{}", type.name(), config);
        }
        return inTime;
    }



}
