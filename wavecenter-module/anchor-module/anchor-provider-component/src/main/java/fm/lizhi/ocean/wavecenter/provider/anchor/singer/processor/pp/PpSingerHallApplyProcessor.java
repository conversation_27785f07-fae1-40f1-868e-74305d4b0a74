package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.remote.IFamilySingHallApplyRemote;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.pp.core.api.LivePpUserService;
import pp.fm.lizhi.live.pp.core.protocol.LivePpUserProto;

import java.util.*;

@Component
@Slf4j
public class PpSingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private LivePpUserService livePpUserService;

    @Autowired
    private IFamilySingHallApplyRemote familySingHallApplyRemote;

    @Override
    public boolean isInSingingHall(Integer appId, Long njId) {
        Long singHallCategoryId = singerAnchorConfig.getPp().getSingHallCategoryId();

        Result<LivePpUserProto.ResponseGetUserCategoryList> result = livePpUserService.getUserCategoryList(
                LivePpUserProto.GetUserCategoryListParams.newBuilder()
                        .setUserId(njId).build()
        );
        if (RpcResult.isFail(result)) {
            log.warn("pp get user category list fail, appId={}, njId={}, rCode:{}", appId, njId, result.rCode());
            return false;
        }

        Boolean isSingHall = Optional.ofNullable(result.target())
                .map(LivePpUserProto.ResponseGetUserCategoryList::getUserCategory)
                .map(LivePpUserProto.UserCategory::getCategoryListList)
                .map(categoryList -> categoryList.stream().anyMatch(category -> singHallCategoryId.equals(category.getId())))
                .orElse(false);

        if (isSingHall) {
            log.info("pp user is in singing hall, appId={}, njId={}", appId, njId);
            return true;
        }

        return familySingHallApplyRemote.isInApply(njId);
    }


    @Override
    public Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> ids) {
        LivePpUserProto.BatchGetUserCategoryListParams params = LivePpUserProto.BatchGetUserCategoryListParams.newBuilder().addAllUserIds(ids).build();
        Result<LivePpUserProto.ResponseBatchGetUserCategoryList> result = livePpUserService.batchGetUserCategoryList(params);
        if (RpcResult.isFail(result)) {
            log.warn("pp batch get user category list fail, appId={}, rCode:{}", appId, result.rCode());
            return new HashMap<>();
        }

        Long singHallCategoryId = singerAnchorConfig.getPp().getSingHallCategoryId();
        LivePpUserProto.ResponseBatchGetUserCategoryList list = result.target();
        Map<Long, Integer> map = new HashMap<>();
        for (LivePpUserProto.UserCategory userCategory : list.getUserCategoryList()) {
            List<LivePpUserProto.WhitelistCategory> categoryListList = userCategory.getCategoryListList();
            if (categoryListList.stream().anyMatch(category -> singHallCategoryId.equals(category.getId()))) {
                map.put(userCategory.getUserId(), SingerHallApplyStatusEnum.APPLYED.getStatus());
            } else {
                //如果没有找到是审核通过，就看看是不是刚申请了品类变更
                if (familySingHallApplyRemote.isInApply(userCategory.getUserId())) {
                    map.put(userCategory.getUserId(), SingerHallApplyStatusEnum.APPLYING.getStatus());
                }
            }
        }
        return map;
    }

    @Override
    public void handleWaitEliminateSingerByHallStatus(List<SingerInfoDTO> singerInfoList, UserInFamilyBean familyBean, Set<Long> eliminateSingerIds, Set<Long> deleteUserTagIds) {

    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }


    @Override
    public boolean isRejectWaitAuditVerify(UserInFamilyBean familyBean) {
        //没有签约新厅，直接拒绝认证申请
        return familyBean == null || familyBean.getNjId() == null || familyBean.getNjId() <= 0;
    }


}
