package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerHallApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;

/**
 * <AUTHOR>
 */
@Component
public class XmSingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Override
    public boolean isInSingingHall(Integer appId, Long njId) {
        SingerSingHallApplyRecord singerHallApplyRecord = singerHallApplyDao.getSingerHallApplyValidRecord(appId, njId);
        return singerHallApplyRecord != null;
    }

    @Override
    public Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> ids) {
       List<Integer> auditStatusList = Lists.newArrayList(SingerHallApplyStatusEnum.APPLYED.getStatus(), SingerHallApplyStatusEnum.APPLYING.getStatus());
       List<SingerSingHallApplyRecord> recordList = singerHallApplyDao.batchGetSingerHallApplyRecordList(appId, ids, auditStatusList);
        if (CollectionUtils.isEmpty(recordList)) {
            return new HashMap<>();
        }
        //转成map, key为njId, value为auditStatus
        return recordList.stream().collect(Collectors.toMap(SingerSingHallApplyRecord::getNjId, SingerSingHallApplyRecord::getAuditStatus));
    }

    @Override
    public void handleWaitEliminateSingerByHallStatus(List<SingerInfoDTO> singerInfoList, UserInFamilyBean familyBean, Set<Long> eliminateSingerIds, Set<Long> deleteUserTagIds) {

    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public boolean isRejectWaitAuditVerify(UserInFamilyBean familyBean) {
         //没有签约新厅，直接拒绝认证申请
         return familyBean == null || familyBean.getNjId() == null || familyBean.getNjId() <= 0;
    }
}
