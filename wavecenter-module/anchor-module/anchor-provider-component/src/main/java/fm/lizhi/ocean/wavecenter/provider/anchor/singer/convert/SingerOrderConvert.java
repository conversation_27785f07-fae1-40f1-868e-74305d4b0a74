package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import org.apache.commons.lang3.StringUtils;

/**
 * 歌手模块的排序转换
 * <AUTHOR>
 */
public class SingerOrderConvert {

    /**
     * 构建排序子句
     */
    public static String buildOrderByClause4RoomDayPageList(String orderMetrics, OrderType orderType) {
        // 设置默认排序
        if (StringUtils.isEmpty(orderMetrics)) {
            return "singer_auth_cnt desc, nj_id asc";
        }

        // 设置默认排序方向
        String direction = OrderType.DESC.getValue();
        if (orderType != null) {
            direction = orderType.getValue();
        }

        // 支持的排序字段映射
        String column;
        switch (orderMetrics) {
            case "singerAuthCnt":
                column = "singer_auth_cnt";
                break;
            case "seniorSingerAuthCnt":
                column = "senior_singer_auth_cnt";
                break;
            case "income":
                column = "last_week_income";
                break;
            default:
                // 不支持的排序字段，使用默认排序
                return "singer_auth_cnt desc, nj_id asc";
        }

        return column + " " + direction + ", nj_id asc";
    }

    /**
     * 获取厅审核列表的排序字段
     */
    public static String getOrderMetricsByPageHallApplyListWithStats(String orderMetrics) {
        if (StringUtils.isEmpty(orderMetrics)) {
            return "modifyTime";
        }
        switch (orderMetrics) {
            case "singerAuthCnt":
                return "singerAuthCnt";
            case "seniorSingerAuthCnt":
                return "seniorSingerAuthCnt";
            case "createTime":
                return "createTime";
            case "applyTime":
                return "applyTime";
            default:
                return "modifyTime";
        }
    }

    /**
     * 构建歌手列表排序子句
     */
    public static String buildOrderByClause4PageSingerInfo(String orderMetrics, OrderType orderType) {
        // 设置默认排序
        if (StringUtils.isEmpty(orderMetrics)) {
            return "audit_time desc, id desc";
        }

        // 设置默认排序方向
        String direction = OrderType.ASC.getValue();
        if (orderType != null) {
            direction = orderType.getValue();
        }

        // 支持的排序字段映射
        String column;
        switch (orderMetrics) {
            case "auditTime":
                column = "audit_time";
                break;
            case "eliminationTime":
                column = "elimination_time";
                break;
            case "createTime":
                column = "create_time";
                break;
            case "modifyTime":
                column = "modify_time";
                break;
            default:
                // 不支持的排序字段，使用默认排序
                return "audit_time desc";
        }

        return column + " " + direction;
    }

    /**
     * 获取审核拉黑列表的排序字段
     */
    public static String getOrderMetricsByPageQuerySingerVerifyRecordWithBlackList(String orderMetrics) {
        if (StringUtils.isEmpty(orderMetrics)) {
            return "create_time";
        }

        if ("auditTime".equals(orderMetrics)) {
            return  "audit_time";
        } else if ("createTime".equals(orderMetrics)) {
            return "create_time";
        }else {
            return "create_time";
        }
    }

    /**
     * 获取审核列表的排序字段
     *
     */
    public static String buildOrderByClause4pageQuerySingerVerifyRecord(String orderMetrics, OrderType orderType) {
        // 设置默认排序
        orderMetrics = getOrderMetricsByPageQuerySingerVerifyRecordWithBlackList(orderMetrics);

        // 设置默认排序方向
        String direction = OrderType.DESC.getValue();
        if (orderType != null) {
            direction = orderType.getValue();
        }

        return orderMetrics + " " + direction;
    }

}
