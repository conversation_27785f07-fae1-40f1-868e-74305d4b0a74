package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.hy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerHallApplyDao;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HySingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Autowired
    private SingerAnchorConfig singerConfig;

    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Override
    public boolean isInSingingHall(Integer appId, Long njId) {
        SingerSingHallApplyRecord singerHallApplyRecord = singerHallApplyDao.getSingerHallApplyValidRecord(appId, njId);
        return singerHallApplyRecord != null;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> ids) {
        List<Integer> auditStatusList = Lists.newArrayList(SingerHallApplyStatusEnum.APPLYED.getStatus(), SingerHallApplyStatusEnum.APPLYING.getStatus());
        List<SingerSingHallApplyRecord> recordList = singerHallApplyDao.batchGetSingerHallApplyRecordList(appId, ids, auditStatusList);
        if (CollectionUtils.isEmpty(recordList)) {
            return new HashMap<>();
        }
        //转成map, key为njId, value为auditStatus
        return recordList.stream().collect(Collectors.toMap(SingerSingHallApplyRecord::getNjId, SingerSingHallApplyRecord::getAuditStatus));
    }

    @Override
    public void handleWaitEliminateSingerByHallStatus(List<SingerInfoDTO> singerInfoList, UserInFamilyBean familyBean, Set<Long> eliminateSingerIds, Set<Long> deleteUserTagIds) {
        if (singerConfig.getHy().isLessRelevanceSwitch()) {
            //弱关联不用判断点唱厅状态
            return;
        }

        //取出userId
        SingerInfoDTO singerInfoDTO = singerInfoList.get(0);
        Long userId = singerInfoDTO.getUserId();
        singerInfoList.forEach(singer -> {
            // 签约同工会，非点唱厅，淘汰
            if (!singerHallApplyManager.isInSingingHall(singerInfoDTO.getAppId(), familyBean.getNjId())) {
                eliminateSingerIds.add(singer.getId());
                deleteUserTagIds.add(userId);
                log.info("user not in singing hall, skip. userId:{}, currentNj:{}, lastNj:{}", userId, familyBean.getNjId(), singer.getNjId());
            }
        });

    }

    @Override
    public boolean isRejectWaitAuditVerify(UserInFamilyBean familyBean) {
        if (familyBean == null) {
            return true;
        }
        //判断是否签约了厅
        boolean isHasHall = familyBean.getNjId() != null && familyBean.getNjId() > 0;
        //如果是强关联，还需要判断是否在点唱厅
        if (!singerConfig.getHy().isLessRelevanceSwitch()) {
            // 签约了厅，非点唱厅，淘汰
            if (isHasHall && !singerHallApplyManager.isInSingingHall(BusinessEvnEnum.HEI_YE.getAppId(), familyBean.getNjId())) {
                return true;
            }
        }

        //没有签约厅，直接拒绝申请
        return !isHasHall;
    }
}
