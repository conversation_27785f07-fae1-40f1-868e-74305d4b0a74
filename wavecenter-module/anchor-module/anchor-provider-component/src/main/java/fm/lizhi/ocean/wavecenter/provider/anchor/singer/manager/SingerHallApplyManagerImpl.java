package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerHallApplyConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerHallApplyDao;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerVerifyApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerSingHallApplyRecordMapper;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.message.manager.SingerPushManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerHallApplyManagerImpl implements SingerHallApplyManager {

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Autowired
    private SingerInfoExtraMapper singerInfoExtraMapper;

    @Autowired
    private SingerSingHallApplyRecordMapper singerSingHallApplyRecordMapper;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private SingerInfoManager singerInfoManager;
    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;
    @Autowired
    private SingerPushManager singerPushManager;
    @Autowired
    private SingerChatManager singerChatManager;
    @Autowired
    private SingerAnchorConfig singerAnchorConfig;


    @Override
    public PageDto<SingerSingHallApplyRecordSummaryBean> pageHallApplyList(PageHallApplyParamDTO param, Integer pageNo,
                                                                           Integer pageSize) {

        // 判断是否需要使用带统计信息的查询方法
        boolean needCustomSort = StrUtil.isNotEmpty(param.getOrderMetrics());

        if (needCustomSort) {
            // 使用新的带统计信息的查询方法（支持所有自定义排序）
            PageList<SingerHallApplyWithStatsDTO> pageList = singerHallApplyDao.pageHallApplyListWithStats(param, pageNo, pageSize);
            if (CollUtil.isEmpty(pageList)) {
                return PageDto.empty();
            }

            // 转换为SummaryBean
            List<SingerSingHallApplyRecordSummaryBean> list = SingerHallApplyConvert.I.convertFromStatsDTOList(pageList);
            return PageDto.of(pageList.getTotal(), list);
        } else {
            // 使用原有的查询方法
            PageList<SingerSingHallApplyRecord> pageList = singerHallApplyDao.pageHallApplyList(param, pageNo, pageSize);
            if (CollUtil.isEmpty(pageList)) {
                return PageDto.empty();
            }
            List<SingerSingHallApplyRecordSummaryBean> list = SingerHallApplyConvert.I.convertSingerSingHallApplyRecordSummaryBeanList(pageList);
            if (CollUtil.isNotEmpty(list)) {
                List<SingerAuthCntDTO> singerAuthCntList = singerInfoExtraMapper.querySingerAuthCnt(param.getAppId(), list.stream().map(SingerSingHallApplyRecordSummaryBean::getNjId).collect(Collectors.toList()));
                // 根据 njId， 把list的singerAuthCnt和seniorSingerAuthCnt填充，singerAuthCnt=singerType=1的数量，seniorSingerAuthCnt=singerType=2和3的数量
                if (CollUtil.isNotEmpty(singerAuthCntList)) {
                    // 按照njId分组，然后根据singer_type统计不同类型的歌手数量
                    Map<Long, List<SingerAuthCntDTO>> singerAuthCntMap = singerAuthCntList.stream()
                            .collect(Collectors.groupingBy(SingerAuthCntDTO::getNjId));

                    list.forEach(bean -> {
                        List<SingerAuthCntDTO> authCntDTOList = singerAuthCntMap.get(bean.getNjId());
                        if (CollUtil.isNotEmpty(authCntDTOList)) {
                            // 统计singerType=1的数量
                            long newSingerCnt = authCntDTOList.stream()
                                    .filter(dto -> SingerTypeEnum.NEW.getType() == dto.getSingerType())
                                    .mapToLong(SingerAuthCntDTO::getCnt)
                                    .sum();

                            // 统计singerType=2和3的数量
                            long seniorSingerCnt = authCntDTOList.stream()
                                    .filter(dto -> SingerTypeEnum.QUALITY.getType() == dto.getSingerType()
                                            || SingerTypeEnum.STAR.getType() == dto.getSingerType())
                                    .mapToLong(SingerAuthCntDTO::getCnt)
                                    .sum();

                            bean.setSingerAuthCnt(newSingerCnt);
                            bean.setSeniorSingerAuthCnt(seniorSingerCnt);
                        }
                    });
                }
            }

            return PageDto.of(pageList.getTotal(), list);
        }
    }

    @Override
    public boolean importHallApply(RequestImportHallApply request) {
        if (CollUtil.isEmpty(request.getNjIds())) {
            log.info("roomIds is empty.");
            return true;
        }

        List<SingerSingHallApplyRecord> importList = buildSingerSingHallApplyRecord(request, request.getSource() == null ?
                SingerHallApplySourceEnum.WAVE_CENTER : request.getSource());
        return singerHallApplyDao.importHallApply(importList);
    }

    @Override
    public List<SingerSingHallApplyRecordBean> getSingerHallApplyRecordByNjIdsAndAppId(List<Long> njIds, int appId) {
        if (CollUtil.isEmpty(njIds)) {
            return new ArrayList<>();
        }

        List<SingerSingHallApplyRecord> singerSingHallApplyRecords = singerHallApplyDao.getSingerHallApplyRecordByNjIdsAndAppId(njIds, appId);
        return SingerHallApplyConvert.I.convertSingerSingHallApplyRecordBeanList(singerSingHallApplyRecords);
    }

    @Override
    public Optional<SingerSingHallApplyRecordBean> passHallApply(RequestOperateHallApply request) {
        if (!SingerHallApplyStatusEnum.APPLYED.equals(request.getStatus())) {
            // 非审核通过类型，不处理
            log.warn("status is not APPLYED. status:{}, id:{}", request.getStatus(), request.getId());
            return Optional.empty();
        }
        SingerSingHallApplyRecord record = singerSingHallApplyRecordMapper
                .selectByPrimaryKey(SingerSingHallApplyRecord.builder()
                        .id(request.getId()).build());
        if (record == null) {
            log.warn("passHallApply apply record not found. id:{}, appId:{}", request.getId(), request.getAppId());
            return Optional.empty();
        }

        //强关联才需要通过歌手审核状态
        if (!singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
            boolean passSingerSuccess = singerInfoManager.passSingerByNjId(new PassSingerByNjParamDTO()
                    .setSingerType(SingerTypeEnum.NEW.getType())
                    .setNjId(record.getNjId())
                    .setOperator(SingerOperatorConstant.SYSTEM)
                    .setAppId(request.getAppId())
                    .setDecorateFlowReason(SingerDecorateOperateReasonConstant.HALL_AUTH_PASS)
            );
            if (!passSingerSuccess) {
                return Optional.empty();
            }
        }

        record.setAuditStatus(request.getStatus().getStatus());
        record.setApplyTime(new Date());
        record.setModifyTime(new Date());
        record.setOperator(request.getOperator());
        // 修改状态
        boolean success = singerSingHallApplyRecordMapper.updateByPrimaryKey(record) > 0;
        return success ? Optional.of(SingerHallApplyConvert.I.convertSingerSingHallApplyRecordBean(record)) : Optional.empty();
    }

    @Override
    public Optional<SingerSingHallApplyRecordBean> rejectHallApply(RequestOperateHallApply request) {
        if (!SingerHallApplyStatusEnum.REJECTED.equals(request.getStatus())) {
            // 非拒绝审核类型，不处理
            log.warn("status is not REJECTED. status:{}, id:{}", request.getStatus(), request.getId());
            return Optional.empty();
        }
        SingerSingHallApplyRecord record = singerSingHallApplyRecordMapper
                .selectByPrimaryKey(SingerSingHallApplyRecord.builder()
                        .id(request.getId()).build());
        if (record == null) {
            log.warn("rejectHallApply apply record not found. id:{}, appId:{}", request.getId(), request.getAppId());
            return Optional.empty();
        }
        record.setAuditStatus(request.getStatus().getStatus());
        record.setDeleted(Boolean.TRUE);
        record.setModifyTime(new Date());
        record.setOperator(request.getOperator());

        if (!singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
            // 除了黑叶，强关联需要淘汰全部歌手
            boolean eliminateRes = singerInfoManager.eliminateSingerByNjId(new EliminationSingerByNjParamDTO()
                    .setAppId(request.getAppId())
                    .setNjId(record.getNjId())
                    .setEliminationReason(SingerEliminationReasonConstant.HALL_CANCEL_QUOTA)
                    .setOperator(SingerOperatorConstant.SYSTEM)
                    .setDecorateFlowReason(SingerDecorateOperateReasonConstant.HALL_QUALIFICATION_CANCEL)
            );
            if (!eliminateRes) {
                return Optional.empty();
            }

            // 更新点唱厅&歌手认证数据
            try {
                ArrayList<Integer> currentAuditStatusList = CollUtil.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(), SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus());
                List<SingerVerifyRecord> verifyRecordList = singerVerifyApplyDao.getSingerVerifyRecordListByNjId(record.getNjId(), currentAuditStatusList);
                log.info("eliminate singer success. appId:{}, njId:{}", request.getAppId(), record.getNjId());
                boolean success = singerHallApplyDao.updateSingerHallAndVerifyApply(record,
                        "【预审核】：厅审核未通过", SingerOperatorConstant.SYSTEM, currentAuditStatusList
                );

                if (success && CollUtil.isNotEmpty(verifyRecordList)) {
                    // 发送推送给用户
                    List<Long> userIds = verifyRecordList.stream().map(SingerVerifyRecord::getUserId).collect(Collectors.toList());
                    singerPushManager.batchPushVerifyStatusChange(request.getAppId(), userIds);
                    //发送私信
                    for (SingerVerifyRecord req2Dto : verifyRecordList) {
                        singerChatManager.sendAuditResultChat(request.getAppId(), req2Dto.getUserId(), req2Dto.getSingerType(), req2Dto.getSongStyle(), SingerChatSceneEnum.PRE_AUDIT_NOT_PASS_BY_SIGN);
                    }
                }

                return success ? Optional.of(SingerHallApplyConvert.I.convertSingerSingHallApplyRecordBean(record)) : Optional.empty();
            } catch (Exception e) {
                log.error("update hall apply record failed. id:{}, appId:{}", request.getId(), request.getAppId(), e);
                return Optional.empty();
            }
        } else {
            boolean success = singerHallApplyDao.updateHallRecord(record);
            return success ? Optional.of(SingerHallApplyConvert.I.convertSingerSingHallApplyRecordBean(record)) : Optional.empty();
        }
    }

    private List<SingerSingHallApplyRecord> buildSingerSingHallApplyRecord(RequestImportHallApply request,
                                                                           SingerHallApplySourceEnum source) {

        if (CollUtil.isEmpty(request.getNjIds())) {
            return new ArrayList<>();
        }

        return request.getNjIds().stream().map(njId -> {
            Optional<FamilyBean> userFamily = familyManager.getUserFamily(njId);
            SingerSingHallApplyRecord record = new SingerSingHallApplyRecord();
            record.setAppId(request.getAppId());
            record.setFamilyId(userFamily.map(FamilyBean::getId).orElse(0L));
            record.setNjId(njId);
            record.setAuditStatus(request.getStatus().getStatus());
            record.setApplyTime(SingerHallApplyStatusEnum.APPLYED.equals(request.getStatus()) ? new Date() : null);
            record.setDeployEnv(ConfigUtils.getEnvRequired().name());
            record.setOperator(request.getOperator());
            record.setDeleted(Boolean.FALSE);
            record.setSource(source.getSource());
            record.setCreateTime(new Date());
            record.setModifyTime(new Date());
            return record;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean isInSingingHall(Integer appId, Long userId) {
        // 查询当前用户的厅ID
        UserInFamilyBean res = familyManager.getUserInFamily(userId);
        if (res == null) {
            return false;
        }

        if (res.isFamily()) {
            // 如果是家族长，直接返回失败
            log.warn("isInSingingHall.user is family. appId={}, userId:{}", appId, userId);
            return false;
        }

        if (res.getNjId() == null || res.getNjId() <= 0) {
            log.warn("isInSingingHall.njId is null. appId={}, userId:{}", appId, userId);
            return false;
        }

        ISingerHallApplyProcessor processor = processorFactory.getProcessor(ISingerHallApplyProcessor.class);
        return processor.isInSingingHall(appId, res.getNjId());
    }

    @Override
    public Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> njIds) {
        ISingerHallApplyProcessor processor = processorFactory.getProcessor(ISingerHallApplyProcessor.class);
        return processor.batchGetSingerHallStatusMap(appId, njIds);
    }


}
