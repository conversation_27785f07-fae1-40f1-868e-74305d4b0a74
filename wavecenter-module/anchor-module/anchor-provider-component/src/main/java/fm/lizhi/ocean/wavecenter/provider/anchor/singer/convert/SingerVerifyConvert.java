package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifySongInfoBean;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyApplySongInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SongInfoDTO;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyApplyDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {UrlUtils.class, ConfigUtils.class})
public abstract class SingerVerifyConvert {

    @Autowired
    protected CommonConfig commonConfig;

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "operator", constant = "")
    @Mapping(target = "auditTime", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "rejectReason", ignore = true)
    @Mapping(target = "remark", constant = "")
    @Mapping(target = "socialVerifyImage", expression = "java(req.getSocialVerifyImageList()==null? \"\": String.join(\",\", req.getSocialVerifyImageList()))")
    @Mapping(target = "audioPath", constant = "")
    @Mapping(target = "songName", constant = "")
    @Mapping(target = "songStyle", constant = "")
    public abstract SingerVerifyRecord DTO2Entity(SingerVerifyApplyDTO req);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "songName", source = "songName")
    @Mapping(target = "audioPath", source = "audioPath")
    @Mapping(target = "songStyle", source = "songStyle")
    @Mapping(target = "preAuditStatus", source = "preAuditStatus")
    @Mapping(target = "applyId", ignore = true)
    @Mapping(target = "appId", expression = "java(appId)")
    @Mapping(target = "userId", expression = "java(userId)")
    public abstract SingerVerifyApplySongInfo songInfo2Entity(SongInfoDTO songInfoDTO, @Context Integer appId, @Context Long userId);

    public abstract List<SingerVerifyApplySongInfo> songInfo2Entities(List<SongInfoDTO> songInfoDTO, @Context Integer appId, @Context Long userId);

    @Mapping(target = "hasPassVerify", constant = "false")
    @Mapping(target = "audioPath", expression = "java(UrlUtils.addCdnHost(commonConfig.getBizConfig(entity.getAppId()).getCdnHost(), entity.getAudioPath()))")
    @Mapping(target = "inBlackList", ignore = true)
    public abstract SingerVerifyRecordDTO entity2DTO(SingerVerifyRecord entity);

    public abstract List<SingerVerifyRecordDTO> entity2DTO(List<SingerVerifyRecord> entities);


    public abstract List<SingerVerifySongInfoBean> songInfo2Bean(List<SingerVerifyApplySongInfo> singerVerifyApplySongInfos);
}
