package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UpdateSingerAuditConfigBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerAuditConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;

@Mapper(unmappedTargetPolicy = ReportingPolicy.WARN,
        imports = {
                ConfigUtils.class
        }
)
public interface SingerAuditConfigConvert {

    SingerAuditConfigConvert I = Mappers.getMapper(SingerAuditConfigConvert.class);

    SingerAuditConfigDTO configToDTO(SingerAuditConfig config);

    List<SingerAuditConfigDTO> configsToDTOs(List<SingerAuditConfig> configs);

    /**
     * 将请求转换为配置
     * @param request 请求
     * @return 配置
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    List<SingerAuditConfig> requestToConfig(List<UpdateSingerAuditConfigBean> request);

    SingerMenuConfigDTO convertSingerMenuConfigDTO(RequestSaveApplyMenuConfig request);

    ResponseApplyMenuConfig convertResponseSaveApplyMenuConfig(SingerMenuConfigDTO dto);
}
