package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerChatProcessor;
import org.springframework.stereotype.Component;

@Component
public class HySingerChatProcessor implements ISingerChatProcessor {
    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean needSendHallAuditingChat(SingerChatSceneEnum scene) {
        return scene != null;
    }
}
