package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecordExample;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOrderConvert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.lizhi.commons.config.core.util.ConfigUtils;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerSingHallApplyRecordMapper;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerHallApplyExtraMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageHallApplyParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerHallApplyWithStatsDTO;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerHallApplyDao {

    @Autowired
    private SingerSingHallApplyRecordMapper singerSingHallApplyRecordMapper;

    @Autowired
    private SingerHallApplyExtraMapper singerHallApplyExtraMapper;

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    /**
     * 分页查询点唱厅列表
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    public PageList<SingerSingHallApplyRecord> pageHallApplyList(PageHallApplyParamDTO param, Integer pageNo, Integer pageSize) {


        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        SingerSingHallApplyRecordExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(param.getAppId())
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (param.getAuditStatus() != null) {
            criteria.andAuditStatusEqualTo(param.getAuditStatus());
        }

        if (param.getNjId() != null) {
            criteria.andNjIdEqualTo(param.getNjId());
        }

        if (param.getStartTime() != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(new Date(param.getStartTime()));
        }

        if (param.getEndTime() != null) {
            criteria.andCreateTimeLessThanOrEqualTo(new Date(param.getEndTime()));
        }

        example.setOrderByClause("modify_time desc");
        return singerSingHallApplyRecordMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 分页查询点唱厅列表（带统计信息）
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    public PageList<SingerHallApplyWithStatsDTO> pageHallApplyListWithStats(PageHallApplyParamDTO param, Integer pageNo, Integer pageSize) {
        // 设置默认排序方向
        OrderType orderDirection = param.getOrderType();
        if (orderDirection == null) {
            orderDirection = OrderType.DESC;
        }

        // 转换时间参数
        Date startTime = param.getStartTime() != null ? new Date(param.getStartTime()) : null;
        Date endTime = param.getEndTime() != null ? new Date(param.getEndTime()) : null;


        if (StringUtils.isEmpty(param.getOrderMetrics())) {
            String orderMetrics = SingerOrderConvert.getOrderMetricsByPageHallApplyListWithStats(param.getOrderMetrics());
            param.setOrderMetrics(orderMetrics);
        }


        // 查询数据
        return singerHallApplyExtraMapper.pageHallApplyListWithStats(
                param.getAppId(),
                param.getAuditStatus(),
                param.getNjId(),
                startTime,
                endTime,
                ConfigUtils.getEnvRequired().name(),
                param.getOrderMetrics(),
                orderDirection.getValue(),
                pageNo,
                pageSize
        );
    }


    /**
     * 根据房间ID和应用ID获取点唱厅申请记录
     */
    public List<SingerSingHallApplyRecord> getSingerHallApplyRecordByNjIdsAndAppId(List<Long> njIds, int appId) {
        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdIn(njIds)
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;
        return singerSingHallApplyRecordMapper.selectByExample(example);
    }

    /**
     * 批量导入点唱厅申请记录
     */
    @Transactional
    public boolean importHallApply(List<SingerSingHallApplyRecord> importList) {
        if (CollUtil.isEmpty(importList)) {
            return true;
        }
        boolean success = singerSingHallApplyRecordMapper.batchInsert(importList) == importList.size();
        if (!success) {
            log.warn("batch insert failed. njIds={}", importList.stream().map(SingerSingHallApplyRecord::getNjId).collect(Collectors.toList()));
            throw new RuntimeException("batch insert failed");
        }

        return true;
    }

    /**
     * 根据厅主ID和应用ID查询点唱厅申请记录
     *
     * @param njId  厅主ID
     * @param appId 应用ID
     * @return 点唱厅申请记录，如果不存在则返回null
     */
    public SingerSingHallApplyRecord getSingerHallApplyValidRecord(int appId, Long njId) {
        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        example.createCriteria()
                .andNjIdEqualTo(njId)
                .andAppIdEqualTo(appId)
                .andAuditStatusNotEqualTo(SingerHallApplyStatusEnum.REJECTED.getStatus())
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<SingerSingHallApplyRecord> recordList = singerSingHallApplyRecordMapper.selectByExample(example);
        //一般一个应用只有一条记录，所以返回第一条
        return recordList.isEmpty() ? null : recordList.get(0);
    }

    /**
     * 批量查询点唱厅申请记录
     *
     * @param appId 应用ID
     * @param njIds 厅主ID列表
     * @return 点唱厅申请记录列表
     */
    public List<SingerSingHallApplyRecord> batchGetSingerHallApplyRecordList(int appId, List<Long> njIds, List<Integer> auditStatusList) {
        SingerSingHallApplyRecordExample example = new SingerSingHallApplyRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdIn(njIds)
                .andDeletedEqualTo(Boolean.FALSE)
                .andAuditStatusIn(auditStatusList)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerSingHallApplyRecordMapper.selectByExample(example);
    }


    /**
     * 更新点唱厅申请记录和歌手认证记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSingerHallAndVerifyApply(SingerSingHallApplyRecord record, String verifyRejectReason, String operator, ArrayList<Integer> currentAuditStatusList) {
        boolean updateHallRes = singerSingHallApplyRecordMapper.updateByPrimaryKey(record) > 0;
        AssertUtil.assertState(updateHallRes, "更新点唱厅申请记录失败");

        singerVerifyApplyDao.updateSingerVerifyRecordStatusByNjId(record.getNjId(),
                currentAuditStatusList,
                SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus(), verifyRejectReason, operator
        );
        return true;
    }

    /**
     * 修改点唱厅状态
     *
     * @param record 记录
     * @return 结果
     */
    public boolean updateHallRecord(SingerSingHallApplyRecord record) {
        return singerSingHallApplyRecordMapper.updateByPrimaryKey(record) > 0;
    }
}
