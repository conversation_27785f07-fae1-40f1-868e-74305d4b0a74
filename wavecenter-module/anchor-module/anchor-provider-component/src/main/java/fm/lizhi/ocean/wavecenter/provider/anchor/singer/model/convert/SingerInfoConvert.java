package fm.lizhi.ocean.wavecenter.provider.anchor.singer.model.convert;


import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoResultBean;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {SingerTypeEnum.class, SingerStatusEnum.class, ConfigUtils.class}
)
public interface SingerInfoConvert {
    SingerInfoConvert INSTANCE = Mappers.getMapper(SingerInfoConvert.class);

    ImportSingerInfoResultBean toImportSingerResultBean(ImportSingerInfoBean bean, String failReason);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "eliminationTime", ignore = true)
    @Mapping(target = "rewardsIssued", ignore = true)
    @Mapping(target = "eliminationReason", constant = "")
    @Mapping(target = "singerVerifyId", constant = "0L")
    @Mapping(target = "userId", source = "bean.singerId")
    @Mapping(target = "singerStatus", expression = "java(SingerStatusEnum.EFFECTIVE.getStatus())")
    @Mapping(target = "modifyTime", source = "importTime")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", source = "importTime")
    @Mapping(target = "auditTime", source = "importTime")
    @Mapping(target = "singerType", expression = "java(bean.getSingerType().getType())")
    SingerInfo toCreateNewImportSinger(ImportSingerInfoBean bean, int appId, String operator, Date importTime);
}
