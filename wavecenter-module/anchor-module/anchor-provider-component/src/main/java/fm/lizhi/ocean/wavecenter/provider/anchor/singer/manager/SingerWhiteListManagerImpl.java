package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerWhiteListConfigMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerWhiteListManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class SingerWhiteListManagerImpl implements SingerWhiteListManager {

    @Autowired
    private SingerWhiteListConfigMapper singerWhiteListConfigMapper;

    @Override
    public List<Long> filterSingerWhiteList(int appId, List<Long> singerIds, SingerTypeEnum singerType) {
        SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
        example.createCriteria().andAppIdEqualTo(appId).andSingerTypeEqualTo(singerType.getType()).andSingerIdIn(singerIds);
        List<SingerWhiteListConfig> singerWhiteListConfigs = singerWhiteListConfigMapper.selectByExample(example);
        return singerWhiteListConfigs.stream().map(SingerWhiteListConfig::getSingerId).collect(Collectors.toList());
    }
}
