package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuthCntDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;


@DataStore(namespace = "mysql_ocean_wavecenter")
public interface SingerInfoExtraMapper {


    @Select("<script>" +
            "SELECT " +
            "  nj_id AS njId, " +
            "  effective_type AS singerType, " +
            "  COUNT(*) AS cnt " +
            "FROM (" +
            "   SELECT " +
            "     nj_id, " +
            "     user_id, " +
            "     MAX(CASE WHEN singer_type IN (2,3) THEN 2 ELSE singer_type END) AS effective_type " +
            "   FROM singer_info " +
            "   WHERE app_id = #{appId} " +
            "     AND nj_id IN " +
            "     <foreach item='id' collection='njIds' open='(' separator=',' close=')'>#{id}</foreach> " +
            "     AND singer_status != 3 " +
            "   GROUP BY nj_id, user_id" +
            ") t " +
            "GROUP BY nj_id, effective_type " +
            "ORDER BY nj_id, effective_type" +
            "</script>")
    List<SingerAuthCntDTO> querySingerAuthCnt(@Param("appId") int appId, @Param("njIds") List<Long> njIds);

    /**
     * 根据应用ID、用户ID、歌手类型修改歌手状态
     */
    @Update("<script>" +
            "UPDATE singer_info " +
            "SET singer_status = #{singerInfo.singerStatus}, " +
            "    elimination_reason = #{singerInfo.eliminationReason}, " +
            "    elimination_time = #{singerInfo.eliminationTime}, " +
            "    operator = #{singerInfo.operator}" +
            "    <if test='singerInfo.auditTime != null'>" +
            "        , audit_time = #{singerInfo.auditTime}" +
            "    </if>" +
            "    <if test='singerInfo.singerVerifyId != null'>" +
            "        , singer_verify_id = #{singerInfo.singerVerifyId}" +
            "    </if>" +
            "    <if test='singerInfo.songStyle != null'>" +
            "        , song_style = #{singerInfo.songStyle}" +
            "    </if>" +
            "    <if test='singerInfo.singerType != null'>" +
            "        , singer_type = #{singerInfo.singerType}" +
            "    </if>" +
            "    <if test='singerInfo.originalSinger != null'>" +
            "        , original_singer = #{singerInfo.originalSinger}" +
            "    </if>" +
            "    <if test='singerInfo.familyId != null'>" +
            "        , family_id = #{singerInfo.familyId}" +
            "    </if>" +
            "    <if test='singerInfo.njId != null'>" +
            "        , nj_id = #{singerInfo.njId}" +
            "    </if>" +
            "WHERE id = #{singerInfo.id} " +
            "AND singer_status = #{currentSingerStatus}" +
            "</script>")
    int updateSingerStatus(@Param("singerInfo") SingerInfo singerInfo,
                          @Param("currentSingerStatus") int currentSingerStatus);

    /**
     * 根据应用ID、用户ID、歌手类型修改歌手状态
     */
    @Update("<script>" +
            "UPDATE singer_info " +
            "SET singer_status = #{singerInfo.singerStatus}, " +
            "    elimination_reason = #{singerInfo.eliminationReason}, " +
            "    elimination_time = #{singerInfo.eliminationTime}, " +
            "    operator = #{singerInfo.operator}" +
            "    <if test='singerInfo.auditTime != null'>" +
            "        , audit_time = #{singerInfo.auditTime}" +
            "    </if>" +
            "    <if test='singerInfo.familyId != null'>" +
            "        , family_id = #{singerInfo.familyId}" +
            "    </if>" +
            "WHERE app_id = #{singerInfo.appId} " +
            "AND user_id = #{singerInfo.userId} " +
            "AND singer_type = #{singerInfo.singerType} " +
            "AND deploy_env = #{singerInfo.deployEnv} " +
            "</script>")
    int updateSingerInfo(@Param("singerInfo") SingerInfo singerInfo);




    /**
     * 统计高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param deployEnv 部署环境
     * @return 高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT user_id) AS total_users " +
            "FROM singer_info " +
            "WHERE app_id = #{appId} " +
            "<if test='singerType != null'>" +
            "AND singer_type = #{singerType} " +
            "</if>" +
            "AND singer_status = #{status} " +
            "AND deploy_env = #{deployEnv} " +
            "</script>")
    long countSingerByStatus(@Param("appId") Integer appId, @Param("status") int status, @Param("deployEnv") String deployEnv, @Param("singerType") Integer singerType);

    /**
     * 统计有收入的高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param env 部署环境
     * @param date 统计日期
     * @return 有收入的高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT si.user_id) AS total_users " +
            "FROM singer_info AS si " +
            "LEFT JOIN singer_data_day AS sdd ON si.user_id = sdd.user_id " +
            "WHERE si.app_id = #{appId} " +
            "<if test='singerType != null'>" +
            "AND si.singer_type = #{singerType} " +
            "</if>" +
            "AND si.singer_status = #{status} " +
            "AND si.deploy_env = #{env} " +
            "AND sdd.stat_date_value = #{date} " +
            "AND sdd.last_day_income > 0 " +
            "</script>"
    )
    long countIncomeSingerByStatDate(@Param("appId") Integer appId, @Param("status") int status, @Param("env") String env, @Param("date") Integer date, @Param("singerType") Integer singerType);

    /**
     * 统计原创高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param deployEnv 部署环境
     * @return 原创高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT user_id) AS total_users " +
            "FROM singer_info " +
            "WHERE app_id = #{appId} " +
            "AND singer_type = #{singerType} " +
            "AND singer_status = #{status} " +
            "AND original_singer = #{originalSinger} " +
            "AND deploy_env = #{deployEnv} " +
            "</script>")
    long countSingerByOriginal(@Param("appId") Integer appId, @Param("status") int status, @Param("deployEnv") String deployEnv, @Param("singerType") Integer singerType, @Param("originalSinger") Boolean originalSinger);

    /**
     * 统计有收入的原创高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param env 部署环境
     * @param date 统计日期
     * @return 有收入的原创高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT si.user_id) AS total_users " +
            "FROM singer_info AS si " +
            "LEFT JOIN singer_data_day AS sdd ON si.user_id = sdd.user_id " +
            "WHERE si.app_id = #{appId} " +
            "AND si.singer_type = #{singerType} " +
            "AND si.singer_status = #{status} " +
            "AND si.original_singer = #{originalSinger} " +
            "AND si.deploy_env = #{env} " +
            "AND sdd.stat_date_value = #{date} " +
            "AND sdd.last_day_income > 0 " +
            "</script>"
    )
    long countIncomeSingerByOriginalAndSingerType(@Param("appId") Integer appId, @Param("status") int status, @Param("env") String env,
                                                  @Param("date") Integer date, @Param("singerType") Integer singerType, @Param("originalSinger") Boolean originalSinger
    );

    @Select("<script>" +
            "SELECT si.* FROM singer_info si LEFT JOIN singer_white_list_config swlc " +
            "  ON si.user_id = swlc.singer_id " +
            "  AND si.singer_type = swlc.singer_type " +
            "  AND si.app_id = swlc.app_id " +
            "  AND si.deploy_env = swlc.deploy_env " +
            "WHERE si.deploy_env = #{deployEnv} " +
            "  AND si.app_id = #{param.appId} " +
            "<if test=\"param.singerType != null\">" +
            "  AND si.singer_type = #{param.singerType.type} " +
            "</if>" +
            "<if test=\"param.njId != null\">" +
            "  AND si.nj_id = #{param.njId} " +
            "</if>" +
            "<if test=\"param.userId != null\">" +
            "  AND si.user_id = #{param.userId} " +
            "</if>" +
            "<if test=\"param.startAuditTime != null\">" +
            "  AND si.audit_time &gt;= #{startAuditTime} " +
            "</if>" +
            "<if test=\"param.endAuditTime != null\">" +
            "  AND si.audit_time &lt;= #{endAuditTime} " +
            "</if>" +
            "<if test=\"param.startEliminationTime != null\">" +
            "  AND si.elimination_time &gt;= #{startEliminationTime} " +
            "</if>" +
            "<if test=\"param.endEliminationTime != null\">" +
            "  AND si.elimination_time &lt;= #{endEliminationTime} " +
            "</if>" +
            "<if test=\"param.singerStatus != null and param.singerStatus.size() > 0\">" +
            "  AND si.singer_status IN " +
            "  <foreach collection=\"param.singerStatus\" item=\"s\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{s.status}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"param.songStyle != null and param.songStyle.size() > 0\">" +
            "  AND (" +
            "    <foreach collection=\"param.songStyle\" item=\"style\" separator=\" OR \">" +
            "      <![CDATA[si.song_style LIKE CONCAT('%', #{style}, '%')]]>" +
            "    </foreach>" +
            "  )" +
            "</if>" +
            "<if test=\"param.originalSinger != null\">" +
            "  AND si.original_singer = #{param.originalSinger} " +
            "</if>" +
            "<if test=\"param.whiteListSinger != null\">" +
            "  <choose>" +
            "    <when test=\"param.whiteListSinger\">" +
            "      AND swlc.id IS NOT NULL " +
            "    </when>" +
            "    <otherwise>" +
            "      AND swlc.id IS NULL " +
            "    </otherwise>" +
            "  </choose>" +
            "</if>" +
            "<if test=\"orderByClause != null and orderByClause != ''\">" +
            "  ORDER BY ${orderByClause}" +
            "</if>" +
            "</script>")
    PageList<SingerInfo> pageSingerInfo(
            @Param("param") PageSingerInfoParamDTO param,
            @Param("deployEnv") String deployEnv,
            @Param("startAuditTime") Date startAuditTime,
            @Param("endAuditTime") Date endAuditTime,
            @Param("startEliminationTime") Date startEliminationTime,
            @Param("endEliminationTime") Date endEliminationTime,
            @Param("orderByClause") String orderByClause,
            @Param(ParamContants.PAGE_NUMBER)int pageNumber,
            @Param(ParamContants.PAGE_SIZE)int pageSize
    );

    // 添加更新方法，用于替代SingerImportManager中的updateByPrimaryKey逻辑
    @Update({
            "<script>",
            "UPDATE singer_info",
            "SET singer_status = #{info.singerStatus},",
            "modify_time = #{info.modifyTime},",
            "nj_id = #{info.njId},",
            "family_id = #{info.familyId},",
            "operator = #{info.operator},",
            "song_style = #{info.songStyle},",
            "original_singer = #{info.originalSinger},",
            "audit_time = #{info.auditTime},",
            "elimination_time = NULL,",
            "elimination_reason = '',",
            "contact_number = #{info.contactNumber}",
            "WHERE id = #{info.id}",
            "</script>"
    })
    int updateSingerInfoForImport(@Param("info") SingerInfo singerInfo);


    /**
     * 查询是否存在歌手记录
     * 歌手类型（singerType）和歌手状态（singerStatus）可以为空
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) FROM singer_info",
            "WHERE app_id = #{appId}",
            "AND user_id = #{userId}",
            "<if test='singerType != null'>",
            "  AND singer_type = #{singerType}",
            "</if>",
            "<if test='singerStatus != null'>",
            "  AND singer_status = #{singerStatus}",
            "</if>",
            "AND deploy_env = #{deployEnv}",
            "</script>"
    })
    int countSingerInfo(@Param("appId") Integer appId,
                        @Param("userId") Long userId,
                        @Param("singerType") Integer singerType,
                        @Param("singerStatus") Integer singerStatus,
                        @Param("deployEnv") String deployEnv);

}
