package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;


import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerExecuteAuditDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.common.job.dispatcher.executor.logger.ExecutorLogger;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.SingerAuditStatusHandler;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.SingerAuditStatusHandlerFactory;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RollbackVerifyStatusJob implements JobHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Autowired
    private SingerAuditStatusHandlerFactory singerAuditStatusHandlerFactory;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        //当前时间往前推7天
        Date endTime = DateUtil.getMinuteBefore(new Date(), singerAnchorConfig.getRollbackVerifyStatusMinTime());
        //结束时间往前推10分钟
        Date startTime = DateUtil.getMinuteBefore(new Date(), singerAnchorConfig.getRollbackVerifyStatusMaxTime());

        SingerAuditParamDTO param = new SingerAuditParamDTO()
                .setOperator(SingerOperatorConstant.SYSTEM).setRejectReason("")
                .setTargetAuditStatus(SingerAuditStatusEnum.WAIT_AUDIT.getStatus());
        for (BusinessEvnEnum businessEvnEnum : BusinessEvnEnum.values()) {
            int successCount = 0;
            //查询审核状态为待决的记录
            List<SingerVerifyRecordDTO> singerVerifyRecordDTOs = singerVerifyApplyManager.getSingerVerifyRecordByTime(businessEvnEnum.getAppId(), startTime, endTime, SingerAuditStatusEnum.WAIT_DECIDE.getStatus());
            for (SingerVerifyRecordDTO singerVerifyRecordDTO : singerVerifyRecordDTOs) {
                //如果修改时间距离当前时间不到
                //如果记录的审核状态为待决，则更新为审核通过
                SingerAuditStatusHandler singerAuditStatusHandler = singerAuditStatusHandlerFactory.getHandler(SingerAuditStatusEnum.WAIT_AUDIT.getStatus());
                param.setSingerType(singerVerifyRecordDTO.getSingerType());
                SingerExecuteAuditDTO res = singerAuditStatusHandler.executeAudit(param, singerVerifyRecordDTO);
                successCount += res.isSuccess() ? 1 : 0;
            }
            ExecutorLogger.getLogger().append("RollbackVerifyStatusJob execute total: {}, successCount: {}, appId: {}", singerVerifyRecordDTOs.size(), successCount, businessEvnEnum.getAppId());
        }
    }


}
