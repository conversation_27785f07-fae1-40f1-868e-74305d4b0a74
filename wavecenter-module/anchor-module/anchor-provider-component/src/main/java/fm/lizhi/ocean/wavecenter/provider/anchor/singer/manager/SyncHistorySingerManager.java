package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.pp.player.api.SingerAuthService;
import pp.fm.lizhi.live.pp.player.enums.SingerAuthAuditStatus;
import pp.fm.lizhi.live.pp.player.protocol.SingerAuthProto;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncHistorySingerManager {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerAuthService singerAuthService;

    @Autowired
    private FamilyManager familyManager;

    public void syncHistorySinger(String param) {
        try {
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            //首次查询的时间设置为2年前
            int successCount = 0;
            int totalCount = 0;
            int needSyncCount = 0;
            int pageSize = StringUtils.isBlank(param) ? 100 : JSONObject.parseObject(param).getInteger("pageSize");
            SingerAuthProto.ResponseBatchGetSingerAuthRecord responseBatchGetSingerAuthRecord = null;
            int pageNo = 1;
            do {
                responseBatchGetSingerAuthRecord = getSingerInfoList(pageNo, pageSize);
                if (responseBatchGetSingerAuthRecord == null) {
                    //失败就结束
                    break;
                }

                List<SingerAuthProto.SingerAuthRecord> recordList = responseBatchGetSingerAuthRecord.getRecordListList();
                //获取userId列表
                List<Long> userIds = recordList.stream().map(SingerAuthProto.SingerAuthRecord::getUserId).distinct().collect(Collectors.toList());
                //先查询是否已经存在了
                List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserIds(userIds, BusinessEvnEnum.PP.getAppId(), SingerStatusEnum.getAllSingerStatus());
                Map<Long, SingerInfoDTO> singerInfoDTOMap = singerInfoList.stream().collect(Collectors.toMap(SingerInfoDTO::getUserId, b -> b, (existingValue, newValue) -> existingValue));
                for (SingerAuthProto.SingerAuthRecord record : recordList) {
                    try {
                        //审核中，但是又是拒绝的，就不落库了
                        if (record.getAuditStatus() == SingerAuthAuditStatus.AUDIT_WAIT ||
                                (!singerInfoDTOMap.containsKey(record.getUserId()) && record.getAuditStatus() == SingerAuthAuditStatus.AUDIT_REJECT)) {
                            continue;
                        }

                        needSyncCount++;
                        Optional<Long> familyId = familyManager.getRoomBestFamilyByCache(record.getNjId());
                        if (singerInfoDTOMap.containsKey(record.getUserId())) {
                            //已经存在了，直接修改
                            UpdateSingerInfoParamDTO updateSingerInfoParamDTO = buildUpdateSingerInfoParamDTO(record, familyId.orElse(0L));
                            boolean res = singerInfoManager.updateSingerInfo(updateSingerInfoParamDTO);
                            successCount += res ? 1 : 0;
                            continue;
                        }

                        SaveSingerInfoParamDTO saveSingerInfoParamDTO = buildSaveSingerInfoParamDTO(record, familyId.orElse(0L));
                        Boolean res = singerInfoManager.saveSingerInfo(saveSingerInfoParamDTO);
                        successCount += res ? 1 : 0;
                        Thread.sleep(10);
                    } catch (Exception e) {
                        log.error("SyncHistorySingerJob.userId fail： userId={}, ", record.getUserId(), e);
                    }
                }
                totalCount += recordList.size();
                pageNo++;
            } while (pageNo <= responseBatchGetSingerAuthRecord.getTotalPage());

            log.info("SyncHistorySingerJob execute successCount: {}, totalCount: {}, needSyncCount:{}", successCount, totalCount, needSyncCount);
        } catch (Exception e) {
            log.error("SyncHistorySingerJob execute fail：", e);
        } finally {
            ContextUtils.clearContext();
        }
    }

    /**
     * 获取历史歌手列表
     *
     * @param pageNo   页码
     * @param pageSize 每页大小
     * @return 结果
     */
    private SingerAuthProto.ResponseBatchGetSingerAuthRecord getSingerInfoList(int pageNo, int pageSize) {
        Result<SingerAuthProto.ResponseBatchGetSingerAuthRecord> result = singerAuthService.batchGetSingerAuthRecord(pageNo, pageSize, SingerAuthAuditStatus.AUDIT_PASS);
        if (RpcResult.isFail(result)) {
            return null;
        }
        return result.target();
    }

    /**
     * 构建保存歌手信息参数
     *
     * @param record   歌手认证记录
     * @param familyId 家族ID
     * @return 保存歌手信息参数
     */
    private SaveSingerInfoParamDTO buildSaveSingerInfoParamDTO(SingerAuthProto.SingerAuthRecord record, Long familyId) {
        return new SaveSingerInfoParamDTO()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setUserId(record.getUserId())
                .setNjId(record.getNjId())
                .setSingerType(SingerTypeEnum.NEW.getType())
                .setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus())
                .setOperator(record.getOperator())
                .setOriginalSinger(false)
                .setContactNumber("")
                .setAuditTime(new Date(record.getAuditTime()))
                .setFamilyId(familyId);
    }

    /**
     * 构建保存歌手信息参数
     *
     * @param record 歌手认证记录
     * @return 保存歌手信息参数
     */
    private UpdateSingerInfoParamDTO buildUpdateSingerInfoParamDTO(SingerAuthProto.SingerAuthRecord record, Long familyId) {
        int singerStatus = record.getAuditStatus() == SingerAuthAuditStatus.AUDIT_PASS ? SingerStatusEnum.EFFECTIVE.getStatus() : SingerStatusEnum.ELIMINATED.getStatus();
        return new UpdateSingerInfoParamDTO()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setUserId(record.getUserId())
                .setSingerType(SingerTypeEnum.NEW.getType())
                .setSingerStatus(singerStatus)
                .setOperator(record.getOperator())
                .setFamilyId(familyId)
                .setEliminationReason(record.getRejectedCause())
                .setOperator(record.getOperator())
                .setAuditTime(new Date(record.getAuditTime()));
    }

}
