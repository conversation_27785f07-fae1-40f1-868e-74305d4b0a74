package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerWhiteListConfigDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SingerWhiteListManager {

    @Resource
    private SingerWhiteListConfigDao singerWhiteListConfigDao;

    /**
     * 判断用户是否在白名单中
     *
     * @param appId      应用ID
     * @param userId     用户ID
     * @param singerType 歌手类型
     * @return 结果
     */
    public boolean isInWhiteList(Integer appId, Long userId, Integer singerType) {
        List<SingerWhiteListConfig> singerWhiteListConfigs = singerWhiteListConfigDao.getSingerWhiteListConfig(appId, userId);
        return singerWhiteListConfigs.stream().anyMatch(config -> config.getSingerType().equals(singerType));
    }

    /**
     * 获取用户的白名单列表
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 白名单列表
     */
    public List<SingerWhiteListConfig> getWhiteList(Integer appId, Long userId) {
        return singerWhiteListConfigDao.getSingerWhiteListConfig(appId, userId);
    }
}
