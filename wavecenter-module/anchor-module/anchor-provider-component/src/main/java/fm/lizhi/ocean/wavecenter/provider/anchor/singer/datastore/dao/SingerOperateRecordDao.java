package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerOperateRecordMapper;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lizhi.commons.config.core.util.ConfigUtils;

@Component
public class SingerOperateRecordDao {

    @Autowired
    private SingerOperateRecordMapper singerOperateRecordMapper;

    /**
     * 查询歌手操作记录
     *
     * @param appId           应用ID
     * @param singerType      歌手类型
     * @param userIds         用户ID列表
     * @param operateTypeList 操作类型
     * @return 歌手操作记录列表
     */
    public List<SingerOperateRecord> getSingerOperateRecordList(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList) {
        if (CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        // 根据应用ID，歌手类型和用户ID列表查询歌手操作记录
        SingerOperateRecordExample example = new SingerOperateRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andSingerTypeEqualTo(singerType)
                .andUserIdIn(userIds)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andOperateTypeIn(operateTypeList);
        example.setOrderByClause("create_time desc");
        return singerOperateRecordMapper.selectByExample(example);
    }

    /**
     * 查询歌手操作记录总数
     *
     * @param appId           应用ID
     * @param singerType      歌手类型
     * @param userIds         用户ID列表
     * @param operateTypeList 从操作类型列表
     * @return 条数
     */
    public Long getSingerOperateRecordCount(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0L;
        }
        // 根据应用ID，歌手类型和用户ID列表查询歌手操作记录
        SingerOperateRecordExample example = new SingerOperateRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andSingerTypeEqualTo(singerType)
                .andUserIdIn(userIds)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andOperateTypeIn(operateTypeList);
        return singerOperateRecordMapper.countByExample(example);
    }
}
