package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.common.job.dispatcher.executor.logger.ExecutorLogger;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager.EliminateSingerManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动淘汰无收入歌手定时任务
 *
 * <AUTHOR>
 */
@Component
public class AutoEliminateNoRevenueSingerJob implements JobHandler {

    @Autowired
    private EliminateSingerManager eliminateSingerManager;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        for (BusinessEvnEnum evnEnum : BusinessEvnEnum.values()) {
            if (evnEnum.getOnline() != 1) {
                continue;
            }
            try {
                ExecutorLogger.getLogger().append("AutoEliminateNoRevenueSingerJob start, appId:{}", evnEnum.getAppId());
                ContextUtils.setBusinessEvnEnum(evnEnum);

                // 处理无营收主播
                try {
                    eliminateSingerManager.autoHandelNoRevenueSinger(evnEnum);
                } catch (Exception e) {
                    ExecutorLogger.getLogger().append("AutoEliminateNoRevenueSingerJob error, appId:{}", evnEnum.getAppId(), e);
                }

            } finally {
                // 无论是否发生异常，最终执行清理
                ExecutorLogger.getLogger().append("AutoEliminateNoRevenueSingerJob end, appId:{}", evnEnum.getAppId());
                ContextUtils.clearContext();
            }
        }

    }
}
