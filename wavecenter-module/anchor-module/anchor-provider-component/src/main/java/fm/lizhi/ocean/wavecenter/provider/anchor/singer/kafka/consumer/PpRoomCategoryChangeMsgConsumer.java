package fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerVerifyApplyDao;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PassSingerByNjParamDTO;
import pp.fm.lizhi.live.pp.whitelist.dto.UserCategoryChangeMsg;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.EliminationSingerByNjParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.pp.util.bean.LiveCategoryObject;
import fm.lizhi.pp.util.utils.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 监听PP 厅品类变更
 * <AUTHOR>
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
public class PpRoomCategoryChangeMsgConsumer {

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;


    /**
     * 厅品类变更消息
     * @param body
     */
    @KafkaHandler(topic = "pp_topic_room_category_change_event", group = "pp_topic_room_category_change_event_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleRoomCategoryChangeMsg(String body){
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.handleRoomCategoryChangeMsg msg={}", msg);
            UserCategoryChangeMsg userCategoryChangeMsg = JsonUtil.loads(msg, UserCategoryChangeMsg.class);
            BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.PP;
            ContextUtils.setBusinessEvnEnum(businessEvnEnum);

            if (userCategoryChangeMsg == null){
                log.warn("handleRoomCategoryChangeMsg userCategoryChangeMsg is null, msg:{}", msg);
                return;
            }

            if (singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
                //弱关联，不自动导入，较长时间需求没有变动，代码可以删除
                return;
            }

            // 为 0 并且上一个品类是点唱厅, 则代表白名单被删除，直接淘汰
            if (userCategoryChangeMsg.getCurrentCategoryId() == 0L && isSingHallCategory(userCategoryChangeMsg.getLastCategoryId())){
                log.warn("handleRoomCategoryChangeMsg currentCategoryId is 0, msg:{}", msg);
                eliminateSingerAndRejectSingerVerify(businessEvnEnum, userCategoryChangeMsg);
                return;
            }

            LiveCategoryObject currentCategory = ConfigUtil.getLiveHallCategoryById(userCategoryChangeMsg.getCurrentCategoryId());
            if(currentCategory == null){
                // 查不到数据, 代表不是大类, 不处理
                log.warn("handleRoomCategoryChangeMsg currentCategory is null, msg:{}", msg);
                return;
            }

            // 点唱厅 --> 其他品类，淘汰
            if (!isSingHallCategory(currentCategory) && isSingHallCategory(userCategoryChangeMsg.getLastCategoryId())){
                log.info("handleRoomCategoryChangeMsg currentCategory is not sing hall, msg:{}", msg);
                eliminateSingerAndRejectSingerVerify(businessEvnEnum, userCategoryChangeMsg);
                return;
            }

            // 是点唱厅, 通过认证
            if (isSingHallCategory(currentCategory)){
                boolean passSingerSuccess = singerInfoManager.passSingerByNjId(new PassSingerByNjParamDTO()
                        .setSingerType(SingerTypeEnum.NEW.getType())
                        .setNjId(userCategoryChangeMsg.getNjId())
                        .setOperator(SingerOperatorConstant.SYSTEM)
                        .setAppId(businessEvnEnum.getAppId())
                        .setDecorateFlowReason(SingerDecorateOperateReasonConstant.HALL_AUTH_PASS)
                );

                log.info("handleRoomCategoryChangeMsg passSinger status={}, msg:{}", passSingerSuccess, msg);
            }

        } catch (Exception e) {
            log.error("handleRoomCategoryChangeMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }


    /**
     * 是否是点唱厅
     */
    private boolean isSingHallCategory(Long categoryId) {
        return Objects.equals(categoryId, singerAnchorConfig.getPp().getSingHallCategoryId());
    }

    private boolean isSingHallCategory(LiveCategoryObject categoryObject) {
        return Objects.equals(categoryObject.getId(), singerAnchorConfig.getPp().getSingHallCategoryId())
                && Objects.equals(categoryObject.getName(), singerAnchorConfig.getPp().getSingRoomCategoryName()
        );
    }

    private void eliminateSingerAndRejectSingerVerify(BusinessEvnEnum businessEvnEnum, UserCategoryChangeMsg userCategoryChangeMsg) {

        // 淘汰歌手
        singerInfoManager.eliminateSingerByNjId(
                new EliminationSingerByNjParamDTO()
                        .setAppId(businessEvnEnum.getAppId())
                        .setNjId(userCategoryChangeMsg.getNjId())
                        .setOperator(SingerOperatorConstant.SYSTEM)
                        .setEliminationReason(SingerEliminationReasonConstant.HALL_CANCEL_QUOTA)
                        .setDecorateFlowReason(SingerDecorateOperateReasonConstant.HALL_CATEGORY_CANCEL)
        );

        // 拒绝认证申请
        singerVerifyApplyDao.updateSingerVerifyRecordStatusByNjId(userCategoryChangeMsg.getNjId(),
                CollUtil.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(), SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus()),
                SingerAuditStatusEnum.REJECTED.getStatus(), SingerEliminationReasonConstant.HALL_CANCEL_QUOTA, SingerOperatorConstant.SYSTEM
        );
    }

}
