package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import java.util.List;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerSingHallApplyRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerHallApplyWithStatsDTO;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.WARN, uses = {CommonConvert.class})
public interface SingerHallApplyConvert {

    SingerHallApplyConvert I = Mappers.getMapper(SingerHallApplyConvert.class);

    @Mapping(target = "singerAuthCnt", constant = "0L")
    @Mapping(target = "seniorSingerAuthCnt", constant = "0L")
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "familyInfo", ignore = true)
    SingerSingHallApplyRecordSummaryBean convertSingerSingHallApplyRecordSummaryBean(SingerSingHallApplyRecord record);
    List<SingerSingHallApplyRecordSummaryBean> convertSingerSingHallApplyRecordSummaryBeanList(PageList<SingerSingHallApplyRecord> pageList);

    List<SingerSingHallApplyRecordBean> convertSingerSingHallApplyRecordBeanList(List<SingerSingHallApplyRecord> singerSingHallApplyRecords);

    SingerSingHallApplyRecordBean convertSingerSingHallApplyRecordBean(SingerSingHallApplyRecord record);

    /**
     * 从带统计信息的DTO转换为SummaryBean
     */
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "familyInfo", ignore = true)
    SingerSingHallApplyRecordSummaryBean convertFromStatsDTO(SingerHallApplyWithStatsDTO statsDTO);

    /**
     * 从带统计信息的DTO列表转换为SummaryBean列表
     */
    List<SingerSingHallApplyRecordSummaryBean> convertFromStatsDTOList(PageList<SingerHallApplyWithStatsDTO> pageList);
}
