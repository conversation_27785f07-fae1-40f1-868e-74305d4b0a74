package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerChatProcessor;
import org.springframework.stereotype.Component;

@Component
public class PpSingerChatProcessor implements ISingerChatProcessor {
    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public boolean needSendHallAuditingChat(SingerChatSceneEnum scene) {
        return scene != null;
    }
}
