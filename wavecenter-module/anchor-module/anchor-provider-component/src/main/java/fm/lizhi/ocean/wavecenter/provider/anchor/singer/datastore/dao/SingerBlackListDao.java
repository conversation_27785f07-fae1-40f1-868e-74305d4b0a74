package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import java.util.List;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerBlackList;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerBlackListExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerBlackListMapper;

@Component
public class SingerBlackListDao {

    @Autowired
    private SingerBlackListMapper singerBlackListMapper;

    /**
     * 根据证件号查询黑名单列表
     *
     * @param idCard 证件号
     * @param appId  应用ID
     * @return 黑名单列表
     */
    public List<SingerBlackList> getSingerBlackListByIdCard(String idCard, Integer appId) {
        // 根据证件号和环境以及appId查询
        SingerBlackListExample example = new SingerBlackListExample();
        example.createCriteria().andIdCardNumberEqualTo(idCard).andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerBlackListMapper.selectByExample(example);
    }

    /**
     * 保存黑名单
     * 
     * @param singerBlackList 黑名单
     * @return 是否保存成功
     */
    public boolean saveSingerBlackList(SingerBlackList singerBlackList) {
        return singerBlackListMapper.insert(singerBlackList) > 0;
    }

    /**
     * 根据用户ID查询黑名单
     * 
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 黑名单
     */
    public SingerBlackList getSingerBlackListByUserId(Integer appId, Long userId) {
        SingerBlackList entity = new SingerBlackList();
        entity.setUserId(userId);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        entity.setAppId(appId);
        return singerBlackListMapper.selectOne(entity);
    }

    /**
     * 根据应用ID和证件号查询黑名单列表
     * 
     * @param appId            应用ID
     * @param idCardNumberList 证件号列表
     * @return 黑名单列表
     */
    public List<SingerBlackList> getSingerBlackListByCertNo(Integer appId, List<String> idCardNumberList) {
        SingerBlackListExample example = new SingerBlackListExample();
        example.createCriteria().andAppIdEqualTo(appId).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andIdCardNumberIn(idCardNumberList);
        return singerBlackListMapper.selectByExample(example);
    }

    /**
     * 取消拉黑
     * 
     * @param appId        应用ID
     * @param idCardNumber 证件号
     * @return 是否保存成功
     */
    public boolean cancelSingerBlackList(Integer appId, String idCardNumber) {
        SingerBlackListExample example = new SingerBlackListExample();
        example.createCriteria().andAppIdEqualTo(appId).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andIdCardNumberEqualTo(idCardNumber);
        return singerBlackListMapper.deleteByExample(example) > 0;
    }

    /**
     * 批量保存黑名单
     * 
     * @param singerBlackListList 黑名单列表
     * @return 是否保存成功
     */
    public boolean batchSaveSingerBlackList(List<SingerBlackList> singerBlackListList) {
        if (CollectionUtils.isEmpty(singerBlackListList)) {
            return true;
        }
        return singerBlackListMapper.batchInsert(singerBlackListList) == singerBlackListList.size();
    }
}
