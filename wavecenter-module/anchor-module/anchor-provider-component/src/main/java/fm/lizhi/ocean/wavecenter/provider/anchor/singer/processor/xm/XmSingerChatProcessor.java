package fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerChatProcessor;
import org.springframework.stereotype.Component;

@Component
public class XmSingerChatProcessor implements ISingerChatProcessor {
    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public boolean needSendHallAuditingChat(SingerChatSceneEnum scene) {
        //厅审核未通过，审核记录通过，则不用发
        return scene != null;
    }
}
