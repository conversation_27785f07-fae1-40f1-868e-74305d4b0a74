package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerAuditChatConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;

@Mapper(unmappedTargetPolicy = ReportingPolicy.WARN,
        imports = {
                ConfigUtils.class
        }
)
public interface SingerChatSceneConvert {

    SingerChatSceneConvert I = Mappers.getMapper(SingerChatSceneConvert.class);

    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    SingerAuditChatConfig dtoToEntity(SingerChatSceneDTO dto);

    SingerChatSceneDTO entityToDto(SingerAuditChatConfig entity);
} 