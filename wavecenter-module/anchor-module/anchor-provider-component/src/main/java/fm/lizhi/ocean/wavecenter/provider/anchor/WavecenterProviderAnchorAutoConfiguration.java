package fm.lizhi.ocean.wavecenter.provider.anchor;

import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/11 14:25
 */
@Configuration
@ComponentScan(basePackages = "fm.lizhi.ocean.wavecenter.provider.anchor")
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.consumer")
public class WavecenterProviderAnchorAutoConfiguration {

    @Bean("publicKafkaTemplate")
    @ConditionalOnMissingBean(name = "publicKafkaTemplate")
    public KafkaTemplate publicKafkaTemplate() {
        return new KafkaTemplate("public-kafka250-bootstrap-server");
    }
}
