package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import com.lark.oapi.core.utils.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerWhiteListConfigMapper;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.producer.SingerKafkaProducer;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.model.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.user.constants.SearchType;
import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
public class SingerImportManager {

    @Autowired
    private SingerInfoMapper singerInfoMapper;
    @Autowired
    private SingerKafkaProducer singerKafkaProducer;
    @Autowired
    private SingerWhiteListConfigMapper singerWhiteListConfigMapper;
    @Autowired
    private UserVerifyManager userVerifyManager;
    @Autowired
    private SingerInfoExtraMapper singerInfoExtMapper;

    @Autowired
    private IdManager idManager;

    public List<SingerInfo> getSingerInfoByUserId(int appId, Long userId, Integer singerType, List<SingerStatusEnum> singerStatusList) {
        SingerInfoExample example = new SingerInfoExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andSingerTypeEqualTo(singerType)
                .andSingerStatusIn(singerStatusList.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()))
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean importSinger(int appId, String operator, ImportSingerInfoBean bean, Date importTime) {
        List<SingerInfo> eliminatedSinger = getSingerInfoByUserId(appId, bean.getSingerId(), bean.getSingerType().getType(), Lists.newArrayList(SingerStatusEnum.ELIMINATED));
        boolean existSinger = CollectionUtils.isNotEmpty(eliminatedSinger);
        log.info("importSinger;appId={};operator={};existSinger={};bean={}", appId, operator, existSinger, bean);
        SingerInfo singer;
        //存在淘汰歌手，修改状态即可
        if(existSinger) {
            singer = new SingerInfo();
            singer.setId(eliminatedSinger.get(0).getId());
            singer.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
            singer.setModifyTime(importTime);
            singer.setNjId(bean.getNjId());
            singer.setFamilyId(bean.getFamilyId());
            singer.setOperator(operator);
            singer.setSongStyle(bean.getSongStyle());
            singer.setOriginalSinger(bean.getOriginalSinger());
            singer.setAuditTime(importTime);
            singer.setEliminationTime(null);
            singer.setRewardsIssued(false);
            singer.setEliminationReason("");
            if(StringUtil.isNotBlank(bean.getContactNumber())) {
                singer.setContactNumber(bean.getContactNumber());
            } else {
                singer.setContactNumber(eliminatedSinger.get(0).getContactNumber());
            }
            if(singerInfoExtMapper.updateSingerInfoForImport(singer) != 1) {
                log.error("importSinger fail;update singer status fail;appId={};operator={};bean={};eliminatedSinger={};", appId, operator, bean, singer.getId());
                return false;
            }
        } else {
            //新增歌手
            singer = SingerInfoConvert.INSTANCE.toCreateNewImportSinger(bean, appId, operator, importTime);
            if(singerInfoMapper.insert(singer) != 1) {
                log.error("importSinger fail;add singer fail;appId={};operator={};bean={};eliminatedSinger={};", appId, operator, bean, singer.getId());
                return false;
            }
        }
        //加入白名单
        SingerWhiteListConfig listConfig = new SingerWhiteListConfig();
        listConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());
        listConfig.setAppId(appId);
        listConfig.setSingerType(bean.getSingerType().getType());
        listConfig.setSingerId(bean.getSingerId());
        SingerWhiteListConfig selected = singerWhiteListConfigMapper.selectOne(listConfig);
        if(selected == null) {
            listConfig.setCreateTime(importTime);
            if(singerWhiteListConfigMapper.insert(listConfig) != 1) {
                throw new RuntimeException("添加白名单失败");
            }
        }
        return true;
    }

    public void sendImportSingerKafkaMessage(int appId, ImportSingerInfoBean bean) {
        singerKafkaProducer.sendImportSingerKafkaMessage(appId, idManager.genId(), bean);
    }

    /**
     * 获取用户的实名认证结果
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 实名认证结果
     */
    public Optional<UserVerifyResultDTO> getVerifyResult(Long userId, Integer appId) {
        // 先查询这个用户的认证结果
        SearchUserVerifyResultParamDTO param = new SearchUserVerifyResultParamDTO()
                .setUserId(userId).setAppId(appId)
                .setAppId(appId)
                .setSearchType(SearchType.RESULT.getValue())
                .setVerifyStatus(VerifyStatusConstant.VERIFY_PASS);
        Result<SearchUserVerifyResultDTO> result = userVerifyManager.searchUserVerifyResult(param);
        if (RpcResult.noBusinessData(result) || CollectionUtils.isEmpty(result.target().getUserVerifyResultList())) {
            // 如果查询实名认证结果失败，则直接返回false
            log.warn("searchUserVerifyResult failed, rCode: {}, req: {}", result.rCode(), JsonUtil.dumps(param));
            return Optional.empty();
        }

        // 一般一个用户只有一条认证 通过的
        return Optional.of(result.target().getUserVerifyResultList().get(0));
    }


}
