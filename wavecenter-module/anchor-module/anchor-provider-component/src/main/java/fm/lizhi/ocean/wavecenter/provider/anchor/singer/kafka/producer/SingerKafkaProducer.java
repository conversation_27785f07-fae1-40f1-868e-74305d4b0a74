package fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.producer;

import fm.lizhi.common.kafka.common.SendResult;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.SingerPassMsg;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerKafkaProducer {

    @Autowired
    private KafkaTemplate publicKafkaTemplate;

    /**
     * 发送消息
     *
     * @param topic 主题
     * @param key   键
     * @param msg   消息
     */
    public boolean send(String topic, String key, String msg) {
        SendResult sendResult = publicKafkaTemplate.send(topic, key, msg);
        if (sendResult.isSuccess()) {
            log.info("SingerKafkaProducer send success - topic:{}, key:{}, msg:{}", topic, key, msg);
        } else {
            log.error("SingerKafkaProducer send fail - topic:{}, key:{}, msg:{}", topic, key, msg);
        }
        return sendResult.isSuccess();
    }

    /**
     * 发送Kafka消息
     *
     * @param appId   业务环境
     */
    public boolean sendImportSingerKafkaMessage(int appId, long transactionId, ImportSingerInfoBean bean) {
        try {
            SingerPassMsg singerPassMsg = new SingerPassMsg()
                    .setAppId(appId)
                    .setSingerIds(Collections.singletonList(bean.getSingerId()))
                    .setSingerType(bean.getSingerType().getType())
                    .setSongStyle(bean.getSongStyle())
                    .setOperator(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())
                    .setReason(SingerDecorateOperateReasonConstant.AUTH_PASS)
                    .setTransactionId(String.valueOf(transactionId));

            String message = JsonUtil.dumps(singerPassMsg);
            return send("lz_topic_singer_pass_msg", String.valueOf(appId), message);
        } catch (Exception e) {
            log.error("sendImportSingerKafkaMessage fail;appId:{}, singerInfoPrimaryKey={}, bean:{}", appId, transactionId, bean, e);
            return false;
        }
    }

}
