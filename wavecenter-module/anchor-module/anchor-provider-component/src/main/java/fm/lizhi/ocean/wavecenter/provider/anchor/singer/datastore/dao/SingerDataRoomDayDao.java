package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataRoomDay;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataRoomDayExample;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOrderConvert;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerDataRoomDayMapper;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerInfoExtraMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手厅日数据
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDataRoomDayDao {

    @Autowired
    private SingerDataRoomDayMapper singerDataRoomDayMapper;

    @Autowired
    private SingerInfoExtraMapper singerInfoExtraMapper;

    /**
     * 分页查询歌手厅数据（支持排序）
     *
     * @param appId        应用ID
     * @param njId         厅主ID
     * @param pageNo       页码
     * @param pageSize     每页大小
     * @param date         日期
     * @param orderMetrics 排序字段
     * @param orderType    排序方向
     * @return 分页数据
     */
    public PageList<SingerDataRoomDay> pageList(Integer appId, Long njId, Integer pageNo, Integer pageSize, Integer date,
                                                String orderMetrics, OrderType orderType) {
        SingerDataRoomDayExample example = new SingerDataRoomDayExample();
        SingerDataRoomDayExample.Criteria criteria = example.createCriteria()
                .andStatDateValueEqualTo(date)
                .andAppIdEqualTo(appId);

        if (njId != null && njId > 0) {
            criteria.andNjIdEqualTo(njId);
        }

        // 设置排序
        String orderByClause = SingerOrderConvert.buildOrderByClause4RoomDayPageList(orderMetrics, orderType);
        example.setOrderByClause(orderByClause);
        return singerDataRoomDayMapper.pageByExample(example, pageNo, pageSize);
    }


    /**
     * 统计有收入的歌手数量
     * @param appId 应用ID
     * @return 有收入的歌手数量
     */
    public long countIncomeSingerByStatDate(Integer appId, Integer date) {
        return singerInfoExtraMapper.countIncomeSingerByStatDate(appId, SingerStatusEnum.EFFECTIVE.getStatus(), ConfigUtils.getEnvRequired().name(), date, null);
    }
}