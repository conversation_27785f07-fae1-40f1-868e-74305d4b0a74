package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 更新歌手签约信息定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncSingerSignJob implements JobHandler {

    @Autowired
    private SingerInfoManager singerInfoManager;


    @Override
    public void execute(JobExecuteContext context) throws Exception {
        if (StringUtils.isEmpty(context.getParam())) {
            log.warn("SyncSingerSignJob param is null");
            return;
        }
        BusinessEvnEnum businessEvnEnum = null;
        try {
            int appId = Integer.parseInt(context.getParam());
            businessEvnEnum = BusinessEvnEnum.from(appId);
            if (null == businessEvnEnum) {
                log.warn("SyncSingerSignJob param is error. param:{}", context.getParam());
                return;
            }
            ContextUtils.reSetBusinessEvnEnum(businessEvnEnum);
        } catch (NumberFormatException e) {
            log.error("SyncSingerSignJob param is not a number");
            return;
        }
        singerInfoManager.syncSingerSign(businessEvnEnum.getAppId());
    }

}
