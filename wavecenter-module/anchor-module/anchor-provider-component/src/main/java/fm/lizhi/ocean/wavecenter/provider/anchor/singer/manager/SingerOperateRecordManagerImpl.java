package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerVerifyApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.BatchBuildSingerOperateRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.BuildSingerOperateRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOperateRecordConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerOperateRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerOperateRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerOperateRecordManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 歌手库操作流水
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerOperateRecordManagerImpl implements SingerOperateRecordManager {

    @Autowired
    private SingerOperateRecordDao singerOperateRecordDao;
    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    @Override
    public List<SingerOperateRecordDTO> getSingerOperateRecordList(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList) {
        // 根据应用ID，歌手类型和用户ID列表查询歌手操作记录
        List<SingerOperateRecord> singerOperateRecordList = singerOperateRecordDao.getSingerOperateRecordList(appId, singerType, userIds, operateTypeList);
        return SingerOperateRecordConvert.I.SingerOperateRecordDTOList(singerOperateRecordList);
    }

    @Override
    public SingerOperateRecordDTO buildSingerOperateRecord(BuildSingerOperateRecordParamDTO param) {
        if (param.getOperateType() == null) {
            return null;
        }
        SingerInfoDTO singerInfo = param.getSingerInfo();
        if (SingerRecordOperateTypeEnum.ELIMINATE.equals(param.getOperateType())
                && SingerStatusEnum.AUTHENTICATING.getStatus() == singerInfo.getSingerStatus()){
            // 认证中 --> 淘汰，直接跳过，不构建歌手操作流水
            log.info("singer {} is authenticating, skip build singer operate record", singerInfo.getUserId());
            return null;
        }
        SingerVerifyRecord verifyRecord = null;
        if (singerInfo != null && singerInfo.getSingerVerifyId() != null && singerInfo.getSingerVerifyId() > 0) {
            verifyRecord = singerVerifyApplyDao.getSingerVerifyRecordById(singerInfo.getSingerVerifyId());
        }

        return SingerOperateRecordConvert.I.buildSingerOperateRecordDto(singerInfo,
                param.getOperateType(),
                verifyRecord,
                param.getOperator(),
                param.getEliminationReason()
        );
    }

    @Override
    public List<SingerOperateRecordDTO> batchBuildSingerOperateRecord(BatchBuildSingerOperateRecordParamDTO param) {
        if (CollUtil.isEmpty(param.getSingerInfoList())) {
            return new ArrayList<>();
        }

        List<Long> verifyIds = param.getSingerInfoList().stream().map(SingerInfoDTO::getSingerVerifyId).filter(id -> id > 0).collect(Collectors.toList());
        Map<Long, SingerVerifyRecord> verifyRecordMap = singerVerifyApplyDao.getSingerVerifyRecordByIds(verifyIds)
                .stream().collect(Collectors.toMap(SingerVerifyRecord::getId, Function.identity()));

        return param.getSingerInfoList().stream().map(singerInfo -> {
            if (SingerRecordOperateTypeEnum.ELIMINATE.equals(param.getOperateType())
                    && SingerStatusEnum.AUTHENTICATING.getStatus() == singerInfo.getSingerStatus()){
                // 认证中 --> 淘汰，直接跳过，不构建歌手操作流水
                log.info("singer {} is authenticating, skip build singer operate record", singerInfo.getUserId());
                return null;
            }

            SingerOperateRecordDTO dto = SingerOperateRecordConvert.I.buildSingerOperateRecordDto(
                    singerInfo,
                    param.getOperateType(),
                    MapUtil.get(verifyRecordMap, singerInfo.getSingerVerifyId(), SingerVerifyRecord.class),
                    param.getOperator(),
                    param.getEliminationReason()
            );
            if (dto != null && dto.getSongName() == null) {
                dto.setSongName("");
            }
            return dto;

        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Long getSingerOperateRecordCount(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList) {
        return singerOperateRecordDao.getSingerOperateRecordCount(appId, singerType, userIds, operateTypeList);
    }
}
