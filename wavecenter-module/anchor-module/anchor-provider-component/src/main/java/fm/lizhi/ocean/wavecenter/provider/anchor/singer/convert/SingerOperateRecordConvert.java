package fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import org.mapstruct.*;

import java.util.Date;
import java.util.List;

import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerOperateRecordDTO;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {Date.class, ConfigUtils.class},
        uses = {CommonConvert.class}
)
public interface SingerOperateRecordConvert {

    SingerOperateRecordConvert I = Mappers.getMapper(SingerOperateRecordConvert.class);


    List<SingerOperateRecordDTO> SingerOperateRecordDTOList(List<SingerOperateRecord> singerOperateRecordList);

    SingerOperateRecordDTO toSingerOperateRecordDTO(SingerOperateRecord singerOperateRecord);

    @Mapping(target = "appId", source = "singerInfo.appId")
    @Mapping(target = "userId", source = "singerInfo.userId")
    @Mapping(target = "familyId", source = "singerInfo.familyId")
    @Mapping(target = "njId", source = "singerInfo.njId")
    @Mapping(target = "songStyle", source = "singerInfo.songStyle")
    @Mapping(target = "originalSinger", source = "singerInfo.originalSinger")
    @Mapping(target = "singerType", source =  "singerInfo.singerType")
    @Mapping(target = "operator", source = "operator")
    @Mapping(target = "songName", source = "verifyRecord.songName")
    @Mapping(target = "operateType", expression = "java(operateType.getOperateType())")
    @Mapping(target = "passTime", source = "singerInfo.auditTime")
    @Mapping(target = "modifyTime",expression = "java(new Date())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "id", ignore = true)
    SingerOperateRecord buildSingerOperateRecord(SingerInfoDTO singerInfo, SingerRecordOperateTypeEnum operateType, SingerVerifyRecord verifyRecord, String operator);

    @Mapping(target = "appId", source = "singerInfo.appId")
    @Mapping(target = "userId", source = "singerInfo.userId")
    @Mapping(target = "familyId", source = "singerInfo.familyId")
    @Mapping(target = "njId", source = "singerInfo.njId")
    @Mapping(target = "songStyle", source = "singerInfo.songStyle")
    @Mapping(target = "originalSinger", source = "singerInfo.originalSinger")
    @Mapping(target = "singerType", source =  "singerInfo.singerType")
    @Mapping(target = "operator", source = "operator")
    @Mapping(target = "songName", source = "verifyRecord.songName")
    @Mapping(target = "eliminationReason", source = "eliminationReason")
    @Mapping(target = "operateType", expression = "java(operateType.getOperateType())")
    @Mapping(target = "passTime", source = "singerInfo.auditTime")
    @Mapping(target = "modifyTime",expression = "java(new Date())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "id", ignore = true)
    SingerOperateRecordDTO buildSingerOperateRecordDto(SingerInfoDTO singerInfo, SingerRecordOperateTypeEnum operateType, SingerVerifyRecord verifyRecord, String operator, String eliminationReason);

    List<SingerOperateRecord> convertOperateRecordList(List<SingerOperateRecordDTO> operateRecordList);

    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    SingerOperateRecord convertOperateRecord(SingerOperateRecordDTO dto);
}
