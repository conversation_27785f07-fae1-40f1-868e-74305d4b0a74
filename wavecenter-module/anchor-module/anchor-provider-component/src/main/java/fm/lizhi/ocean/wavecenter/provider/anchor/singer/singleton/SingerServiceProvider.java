package fm.lizhi.ocean.wavecenter.provider.anchor.singer.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanOceanStandardAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.pp.family.api.FamilySingHallApplyService;
import fm.pp.family.api.NewHallService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pp.fm.lizhi.live.pp.core.api.LivePpUserService;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/04/09 14:42
 */
@Configuration
@ScanOceanStandardAPI(values = {
})
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = xm.fm.lizhi.live.pp.vocal.api.VocalSingerVerifyService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LivePpUserService.class),
})
public class SingerServiceProvider {

    @Bean
    public NewHallService ppNewHallService(){
        return new DubboClientBuilder<>(NewHallService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public FamilySingHallApplyService ppFamilySingHallApplyService(){
        return new DubboClientBuilder<>(FamilySingHallApplyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }
}
