package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataRoomDay;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyApplySongInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerDataRoomDayConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOperateRecordConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerOperateRecordManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.SingerPushManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 歌手库管理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerInfoManagerImpl implements SingerInfoManager {

    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerInfoMapper singerInfoMapper;

    @Autowired
    private SingerRedisManagerImpl singerRedisManager;

    @Autowired
    private SingerDataRoomDayDao singerDataRoomDayDao;

    @Autowired
    private SingerDecorateManager singerDecorateManager;

    @Autowired
    private SingerOperateRecordManager singerOperateRecordManager;

    @Autowired
    private SingerChatManager singerChatManager;

    @Autowired
    private SingerPushManager singerPushManager;


    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private SingerWhiteListConfigDao singerWhiteListConfigDao;
    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;
    @Autowired
    private SingerVerifyApplySongInfoDao singerVerifyApplySongInfoDao;
    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private FamilyManager familyManager;


    @Override
    public Boolean saveSingerInfo(SaveSingerInfoParamDTO param) {
        SingerInfo singerInfo = SingerInfoConvert.I.buildSingerInfo(param);
        return singerInfoMapper.insert(singerInfo) > 0;
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoByNjId(int appId, Long njId, SingerTypeEnum singerType, SingerStatusEnum singerStatus) {
        List<SingerInfo> singerInfoList = singerInfoDao.selectSingerInfoByNjId(appId, njId, singerType,
                singerStatus == null ? null : CollUtil.newArrayList(singerStatus)
        );
        return SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoList);
    }

    @Override
    public boolean passSingerByNjId(PassSingerByNjParamDTO param) {
        List<SingerInfoDTO> authenticatingSingerInfoList = getSingerInfoByNjId(param.getAppId(), param.getNjId(), SingerTypeEnum.getByType(param.getSingerType()), SingerStatusEnum.AUTHENTICATING);
        if (CollUtil.isEmpty(authenticatingSingerInfoList)) {
            log.info("passSingerByNjId no authenticating singer. njId:{}, appId:{}", param.getNjId(), param.getAppId());
            return true;
        }

        try (RedisLock lock = singerRedisManager.tryGetSingerUpdateLock(param.getAppId(), param.getNjId())) {
            if (!lock.tryLock()) {
                log.warn("passSingerByNjId lock failed. njId:{}, appId:{}", param.getNjId(), param.getAppId());
                //加锁失败，结束
                return false;
            }
            // 更改歌手状态 --> 认证通过
            authenticatingSingerInfoList.forEach(singer -> {
                singer.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
                singer.setAuditTime(new Date().getTime());
                singer.setModifyTime(new Date().getTime());
            });

            // 构建发放装扮流水
            Optional<SingerDecorateFlowGenerateDTO> optional = singerDecorateManager.generateSingerDecorateFlowList(new SingerDecorateFlowInitParamDTO()
                    .setAppId(param.getAppId())
                    .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                    .setReason(SingerDecorateOperateReasonConstant.HALL_AUTH_PASS)
                    .setOperator(param.getOperator())
                    .setSingerInfoList(authenticatingSingerInfoList)
            );

            List<SingerDecorateFlow> flowList = optional.map(SingerDecorateFlowGenerateDTO::getDecorateFlowList)
                    .map(SingerDecorateConvert.I::toSingerDecorateFlowList)
                    .orElse(new ArrayList<>());

            // 构建操作流水
            List<SingerOperateRecordDTO> operateRecordList = singerOperateRecordManager.batchBuildSingerOperateRecord(
                    new BatchBuildSingerOperateRecordParamDTO().setOperator(param.getOperator())
                            .setOperateType(SingerRecordOperateTypeEnum.PASS)
                            .setSingerInfoList(authenticatingSingerInfoList)
            );

            // 通过歌手并写入发放装扮流水
            Boolean success = singerInfoDao.passSinger(SingerInfoConvert.I.convertSingerInfoList(authenticatingSingerInfoList),
                    flowList, SingerOperateRecordConvert.I.convertOperateRecordList(operateRecordList)
            );
            if (!success) {
                return false;
            }

            if (optional.isPresent()) {
                // 调用操作装扮方法
                singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(optional.get().getTransactionId()));
                log.info("passSingerByNjId success. transactionId:{}", optional.get().getTransactionId());

                // 发送私信
                singerChatManager.batchSendAuditResultChatAsync(param.getAppId(), authenticatingSingerInfoList, SingerChatSceneEnum.SINGER_AUDIT_PASS);

                List<Long> singerIds = authenticatingSingerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
                singerPushManager.batchPushVerifyStatusChange(param.getAppId(), singerIds);

            }

        } catch (Exception e) {
            log.error("passSingerByNjId lock error. njId:{}, appId:{}", param.getNjId(), param.getAppId(), e);
            return false;
        }
        return true;
    }


    @Override
    public boolean eliminateSingerByNjId(EliminationSingerByNjParamDTO param) {
        List<SingerInfo> singerInfoList = singerInfoDao.selectSingerInfoByNjId(param.getAppId(), param.getNjId(), null,
                CollUtil.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING));

        if (CollUtil.isEmpty(singerInfoList)) {
            return true;
        }
        List<SingerInfoDTO> infoDTOList = SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoList);
        //查询出白名单中的歌手
        List<SingerWhiteListConfig> whiteList = singerWhiteListConfigDao.batchGetSingerWhiteList(infoDTOList);
        //移除在白名单中，且歌手类型一致的歌手
        infoDTOList.removeIf(item -> whiteList.stream()
                .anyMatch(white -> Objects.equals(white.getSingerId(), item.getUserId()) && Objects.equals(white.getSingerType(), item.getSingerType())));

        //@what 黑叶不管强弱关联，高级歌手都不会因为厅的原因被淘汰
        //@why 直接移去掉黑叶的高级歌手 不搞processor了
        if (param.getAppId() == BusinessEvnEnum.HEI_YE.appId()) {
            infoDTOList.removeIf(singerInfoDTO -> singerInfoDTO.getSingerType() >= SingerTypeEnum.QUALITY.getType());
            log.info("eliminateSingerByNjId protect quality singer. njId:{}, appId:{}, infoDTOList={}", param.getNjId(), param.getAppId(), JsonUtils.toJsonString(infoDTOList));
        }

        if (CollectionUtils.isEmpty(infoDTOList)) {
            return true;
        }
        try (RedisLock lock = singerRedisManager.tryGetSingerUpdateLock(param.getAppId(), param.getNjId())) {
            if (!lock.tryLock()) {
                log.warn("eliminateSingerByNjId lock failed. njId:{}, appId:{}", param.getNjId(), param.getAppId());
                //加锁失败，结束
                return false;
            }
            return recoverAndEliminateSinger(param.getAppId(), param.getOperator(), param.getEliminationReason(),
                    SingerDecorateOperateReasonConstant.HALL_QUALIFICATION_CANCEL,
                    infoDTOList, false
            );
        } catch (Exception e) {
            log.error("eliminateSingerByNjId lock error. njId:{}, appId:{}", param.getNjId(), param.getAppId(), e);
            return false;
        }
    }

    /**
     * 回收并淘汰歌手
     *
     * @param singerInfoList 歌手信息列表
     * @param isManual       是否手动操作
     * @return 结果
     */
    private Boolean recoverAndEliminateSinger(int appId, String operator,
                                              String eliminationReason,
                                              String decorateOperateReason,
                                              List<SingerInfoDTO> singerInfoList,
                                              Boolean isManual
    ) {
        // 构件回收装扮流水
        Optional<SingerDecorateFlowGenerateDTO> optional = singerDecorateManager.generateSingerDecorateFlowList(new SingerDecorateFlowInitParamDTO()
                .setAppId(appId)
                .setOperateType(SingerDecorateFlowOperateEnum.RECOVER)
                .setReason(decorateOperateReason)
                .setOperator(operator)
                .setSingerInfoList(singerInfoList)
        );
        List<SingerDecorateFlow> flowList = optional.map(SingerDecorateFlowGenerateDTO::getDecorateFlowList)
                .map(SingerDecorateConvert.I::toSingerDecorateFlowList)
                .orElse(new ArrayList<>());

        // 构建操作流水
        List<SingerOperateRecordDTO> operateRecordList = singerOperateRecordManager.batchBuildSingerOperateRecord(
                new BatchBuildSingerOperateRecordParamDTO().setOperator(operator)
                        .setOperateType(SingerRecordOperateTypeEnum.ELIMINATE)
                        .setSingerInfoList(singerInfoList).setEliminationReason(eliminationReason)
        );

        try {
            // 手动淘汰时，需要删除对应等级的白名单配置
            List<SingerWhiteListConfig> whiteListConfigs = isManual ? singerWhiteListConfigDao.batchGetSingerWhiteList(singerInfoList) : Collections.emptyList();
            log.info("recoverAndEliminateSinger.whiteListConfigs:{},isManual:{}", whiteListConfigs, isManual);
            //取出歌手ID
            List<Long> userIds = singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
            //查出待审核的高级歌手认证记录，自动淘汰的话需要把认证记录也设置为不通过
            List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.batchGetSingerVerifyRecordListByStatus(appId, userIds,
                    Lists.newArrayList(SingerTypeEnum.QUALITY.getType(), SingerTypeEnum.STAR.getType()),
                    Lists.newArrayList(SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.WAIT_AUDIT.getStatus()));
            log.info("recoverAndEliminateSinger.singerVerifyRecords:{}", singerVerifyRecords);
            // 淘汰歌手并写入回收装扮流水
            Boolean success = singerInfoDao.eliminateSinger(SingerInfoConvert.I.convertSingerInfoList(singerInfoList),
                    eliminationReason, flowList, SingerOperateRecordConvert.I.convertOperateRecordList(operateRecordList),
                    operator, whiteListConfigs, singerVerifyRecords, isManual
            );
            log.info("recoverAndEliminateSinger.eliminateSinger res:{}", success);
            if (!success) {
                return false;
            }
            if (optional.isPresent()) {
                // 调用回收方法
                singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO()
                        .setTransactionId(optional.get().getTransactionId())
                );
                log.info("recoverAndEliminateSinger success. transactionId:{}", optional.get().getTransactionId());
            }

            // 发送私信
            singerChatManager.batchSendAuditResultChatAsync(appId, singerInfoList,
                    isManual ? SingerChatSceneEnum.MANUAL_DISQUALIFICATION : SingerChatSceneEnum.AUTO_DISQUALIFICATION
            );

            List<Long> singerIds = singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
            singerPushManager.batchPushVerifyStatusChange(appId, singerIds);

            return true;
        } catch (Exception e) {
            log.error("recoverAndEliminateSinger error. userIds:{}",
                    singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList()), e);
            return false;
        }
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoByUserId(int appId, Long userId, SingerStatusEnum singerStatus) {
        List<SingerInfo> singerInfoList = singerInfoDao.getSingerInfoByUserId(appId, userId, singerStatus);
        return SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoList);
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoByUserId(int appId, Long userId, List<SingerStatusEnum> singerStatusList) {
        List<SingerInfo> singerInfoList = singerInfoDao.getSingerInfoByUserId(appId, userId, singerStatusList);
        return SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoList);
    }

    @Override
    public SingerInfoDTO getSingerInfo(int appId, Long userId, Integer singerType) {
        SingerInfo singerInfo = singerInfoDao.getSingerInfo(appId, userId, singerType);
        return SingerInfoConvert.I.convertSingerInfoDTO(singerInfo);
    }

    @Override
    public boolean isSinger(Integer appId, Long userId) {
        // 先查缓存
        Boolean isSinger = singerRedisManager.isSinger(appId, userId);
        if (isSinger != null) {
            return isSinger;
        }

        // 缓存未命中，查库
        boolean result = singerInfoDao.isSinger(appId, userId);

        // 写入缓存
        singerRedisManager.setIsSinger(appId, userId, result);
        return result;
    }

    @Override
    public SingerCountInHallDTO singerTotalCountInHall(Integer appId, Long njId) {
        // 先查缓存
        SingerCountInHallDTO count = singerRedisManager.getSingerTotalCountInHall(appId, njId);
        if (count != null) {
            return count;
        }

        // 缓存未命中，查库
        SingerCountInHallDTO result = singerInfoDao.countSingerInHall(njId);

        // 写入缓存
        singerRedisManager.setSingerTotalCountInHall(appId, njId, result);

        return result;
    }

    @Override
    public PageBean<SingerRoomDetailDTO> singerRoomDetails(Integer appId, Long njId, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType) {

        Integer current = MyDateUtil.getDateDayValue(new Date());
        PageList<SingerDataRoomDay> pageList = singerDataRoomDayDao.pageList(appId, njId, pageNo, pageSize, current, orderMetrics, orderType);
        if (CollUtil.isEmpty(pageList)) {
            return PageBean.empty();
        }

        List<SingerRoomDetailDTO> dtoList = SingerDataRoomDayConvert.I.convertToDTOList(pageList);
        return PageBean.of(pageList.getTotal(), dtoList);
    }

    @Override
    public ResponseGetAllSingerStatics getAllSingerStatics(Integer appId) {

        ResponseGetAllSingerStatics result = new ResponseGetAllSingerStatics();

        // 获取的所有歌手数量
        long allSingerCnt = singerInfoDao.countAllSingerByStatDate(appId);

        // 获取有收入的歌手数量
        long allIncomeSingerCnt = singerDataRoomDayDao.countIncomeSingerByStatDate(appId, MyDateUtil.getDateDayValue(new Date()));
        result.setAllSingerCnt((int) allSingerCnt).setAllIncomeSingerCnt((int) allIncomeSingerCnt);

        ISingerProcessor processor = processorFactory.getProcessor(ISingerProcessor.class);
        return processor.fillSingerStatics(appId, result);

    }

    @Override
    public PageBean<SingerInfoDTO> pageSingerInfo(PageSingerInfoParamDTO param) {

        PageList<SingerInfo> singerList = singerInfoDao.pageSingerInfo(param);
        if (CollUtil.isEmpty(singerList)) {
            return PageBean.empty();
        }

        List<SingerInfoDTO> dtoList = SingerInfoConvert.I.convertSingerInfoDTOList(singerList);
        return PageBean.of(singerList.getTotal(), dtoList);
    }

    @Override
    public Boolean eliminateSinger(int appId, List<Long> ids, String operator, String eliminateReason, Boolean isManual) {
        List<SingerInfo> singerInfoList = singerInfoDao.getSingerInfoByIds(appId, ids);
        if (CollUtil.isEmpty(singerInfoList)) {
            return true;
        }

        // 将singerInfoList按 njId 分组，并进行加锁，调用
        Map<Long, List<SingerInfo>> njIdToSingerInfoMap = singerInfoList.stream()
                .collect(Collectors.groupingBy(SingerInfo::getNjId));

        boolean allSuccess = true;
        for (Map.Entry<Long, List<SingerInfo>> entry : njIdToSingerInfoMap.entrySet()) {
            Long njId = entry.getKey();
            List<SingerInfo> singerInfosInNj = entry.getValue();

            try (RedisLock lock = singerRedisManager.tryGetSingerUpdateLock(appId, njId)) {
                if (!lock.tryLock()) {
                    log.warn("eliminateSinger lock failed. njId:{}, appId:{}", njId, appId);
                    allSuccess = false;
                    continue;
                }

                boolean success = recoverAndEliminateSinger(appId, operator, eliminateReason,
                        SingerDecorateOperateReasonConstant.SINGER_ELIMINATED,
                        SingerInfoConvert.I.convertSingerInfoDTOList(singerInfosInNj), isManual);

                if (!success) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("eliminateSinger lock error. njId:{}, appId:{}", njId, appId, e);
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    @Override
    public Boolean upgradeSinger(int appId, List<Long> ids, SingerTypeEnum singerType, String operator) {
        List<SingerInfo> singerInfoList = singerInfoDao.getSingerInfoByIds(appId, ids);
        if (CollUtil.isEmpty(singerInfoList)) {
            return true;
        }

        return grantAndUpgradeSinger(appId, operator, singerType, SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoList),
                SingerDecorateOperateReasonConstant.UPGRADE);
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoByUserIds(List<Long> userIds, int appId, List<SingerStatusEnum> singerStatusList) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<SingerInfo> list = singerInfoDao.getSingerInfoByUserIds(appId, userIds, singerStatusList);
        return SingerInfoConvert.I.convertSingerInfoDTOList(list);
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoByUserIds(List<Long> userIds, int appId, List<SingerStatusEnum> singerStatusList, SingerTypeEnum singerType) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<SingerInfo> list = singerInfoDao.getSingerInfoByUserIds(appId, userIds, singerStatusList, singerType);
        return SingerInfoConvert.I.convertSingerInfoDTOList(list);
    }

    @Override
    public void addEliminateSingerTag(int appId, Long userId) {
        boolean res = singerRedisManager.addEliminateSingerTag(appId, userId);
        log.info("addEliminateSingerTag appId:{}, userId:{}, res:{}", appId, userId, res);
    }

    @Override
    public boolean updateSingerInfo(UpdateSingerInfoParamDTO param) {
        return singerInfoDao.updateSingerInfo(param);
    }


    @Override
    public List<SingerInfoDTO> getRewardsIssuedByUserIds(List<Long> userIds, int appId, boolean rewardsIssued) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<SingerInfo> list = singerInfoDao.getRewardsIssuedByUserIds(appId, userIds, rewardsIssued);
        return SingerInfoConvert.I.convertSingerInfoDTOList(list);
    }

    @Override
    public boolean isCanApplySingerVerify(int appId, Long userId, Integer singerType) {
        List<SingerStatusEnum> singerStatusList = Lists.newArrayList(SingerStatusEnum.AUTHENTICATING, SingerStatusEnum.EFFECTIVE);
        List<SingerInfo> singerInfo = singerInfoDao.getSingerInfoByUserId(appId, userId, singerStatusList);
        //如果是申请普通歌手，但是又没有在歌手库，就可以申请
        if (CollectionUtils.isEmpty(singerInfo) && SingerTypeEnum.isPrimaryType(singerType)) {
            return true;
        }

        List<SingerInfo> lowLeveSinger = singerInfo.stream().filter(info -> info.getSingerType() < singerType).collect(Collectors.toList());
        Optional<SingerInfo> auditingSingerInfo = lowLeveSinger.stream().filter(info -> info.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus()).findFirst();
        if (CollectionUtils.isEmpty(lowLeveSinger) || auditingSingerInfo.isPresent()) {
            //都申请高级歌手了，竟然没有在低级歌手库或者低等级歌手还在认证中，也不给申请高等级歌手
            log.info("isCanApplySingerVerify.不允许跨等级申请，userId:{} is not singer, singerType:{}", userId, singerType);
            return false;
        }

        //是否已经是当前等级的歌手了
        Optional<SingerInfo> first = singerInfo.stream().filter(info -> Objects.equals(info.getSingerType(), singerType)).findFirst();
        //已经是当前类型的歌手了，不能再申请了
        return !first.isPresent();
    }

    @Override
    public List<SingerInfoDTO> getSingerInfoListByType(int appId, Long userId, List<Integer> singerTypeList) {
        if (CollectionUtils.isEmpty(singerTypeList)) {
            return new ArrayList<>();
        }
        List<SingerInfo> singerInfoListByType = singerInfoDao.getSingerInfoListByType(appId, userId, singerTypeList);
        return SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoListByType);
    }

    /**
     * 晋升歌手并发放装扮
     *
     * @param singerInfoList 歌手信息列表
     * @return
     */
    private Boolean grantAndUpgradeSinger(int appId, String operator, SingerTypeEnum singerType, List<SingerInfoDTO> singerInfoList, String decorateOperateReason) {

        // 已存在的歌手信息
        List<SingerInfo> existSingerInfoByUpgrade = buildExistSingerInfoByUpgrade(appId, operator, singerType,
                singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList())
        );
        List<Long> existSingerInfoIds = existSingerInfoByUpgrade.stream().map(SingerInfo::getUserId).collect(Collectors.toList());

        // 构建晋升歌手信息
        List<SingerInfo> upgradeSinferList = singerInfoList.stream()
                .filter(singer -> !CollUtil.safeContains(existSingerInfoIds, singer.getUserId()))
                .map(info -> {
                    SingerInfo upgradeSingerInfo = new SingerInfo();
                    BeanUtils.copyProperties(info, upgradeSingerInfo);
                    upgradeSingerInfo.setId(null);
                    upgradeSingerInfo.setSingerType(singerType.getType());
                    upgradeSingerInfo.setRewardsIssued(false);
                    upgradeSingerInfo.setOperator(operator);
                    upgradeSingerInfo.setAuditTime(new Date(info.getAuditTime()));
                    upgradeSingerInfo.setDeployEnv(ConfigUtils.getEnvRequired().name());
                    upgradeSingerInfo.setCreateTime(new Date());
                    upgradeSingerInfo.setModifyTime(new Date());
                    return upgradeSingerInfo;
                }).collect(Collectors.toList());

        // 全部晋升的歌手，包含数据库已存在和未存在的，用于记录用户的操作记录和装扮流水生成
        List<SingerInfo> allUpgradeSingerInfo = Stream.of(existSingerInfoByUpgrade, upgradeSinferList)
                .flatMap(List::stream)
                .collect(Collectors.toList());


        // 构件发放装扮流水
        Optional<SingerDecorateFlowGenerateDTO> optional = singerDecorateManager.generateSingerDecorateFlowList(new SingerDecorateFlowInitParamDTO()
                .setAppId(appId)
                .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                .setReason(decorateOperateReason)
                .setOperator(operator)
                .setSingerInfoList(SingerInfoConvert.I.convertSingerInfoDTOList(allUpgradeSingerInfo))
        );
        List<SingerDecorateFlow> flowList = optional.map(SingerDecorateFlowGenerateDTO::getDecorateFlowList)
                .map(SingerDecorateConvert.I::toSingerDecorateFlowList)
                .orElse(new ArrayList<>());

        // 构建操作流水
        List<SingerOperateRecordDTO> operateRecordList = singerOperateRecordManager.batchBuildSingerOperateRecord(
                new BatchBuildSingerOperateRecordParamDTO().setOperator(operator)
                        .setOperateType(SingerRecordOperateTypeEnum.UPGRADE)
                        .setSingerInfoList(singerInfoList)
        );

        // 晋升歌手并写入发放装扮流水
        Boolean success = singerInfoDao.upgradeSinger(
                upgradeSinferList, existSingerInfoByUpgrade, flowList, SingerOperateRecordConvert.I.convertOperateRecordList(operateRecordList)
        );
        if (!success) {
            return false;
        }
        if (optional.isPresent()) {
            // 调用操作装扮方法
            singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(optional.get().getTransactionId()));
            log.info("grantAndUpgradeSinger success. transactionId:{}", optional.get().getTransactionId());
        }

        return true;
    }

    /**
     * 构建存在的歌手晋升信息
     *
     * @param appId
     * @param operator
     * @param singerType
     * @return
     */
    private List<SingerInfo> buildExistSingerInfoByUpgrade(int appId, String operator, SingerTypeEnum singerType, List<Long> singerInfoUserIds) {
        // 获取当前等级存在的歌手信息
        List<SingerInfoDTO> updateSingerInfoList = getSingerInfoByUserIds(
                singerInfoUserIds,
                appId,
                null,
                singerType
        );

        if (CollUtil.isNotEmpty(updateSingerInfoList)) {
            updateSingerInfoList.forEach(singer -> {
                singer.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
                singer.setRewardsIssued(false);
                singer.setOperator(operator);
                singer.setModifyTime(new Date().getTime());
            });
        }
        return SingerInfoConvert.I.convertSingerInfoList(updateSingerInfoList);
    }

    /**
     * 根据ID列表获取
     */
    @Override
    public List<SingerInfoDTO> getSingerInfoByIds(int appId, List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }

        return SingerInfoConvert.I.convertSingerInfoDTOList(singerInfoDao.getSingerInfoByIds(appId, ids));
    }

    @Override
    public PageBean<SingerInfoDTO> pageSingerInfoByStatusAndType(int appId, SingerStatusEnum singerStatus, SingerTypeEnum singerType, int pageNo, int pageSize) {
        PageList<SingerInfo> singerList = singerInfoDao.pageSingerInfoByStatusAndType(appId, singerStatus, singerType, pageNo, pageSize);
        if (CollUtil.isEmpty(singerList)) {
            return PageBean.empty();
        }

        List<SingerInfoDTO> dtoList = SingerInfoConvert.I.convertSingerInfoDTOList(singerList);
        return PageBean.of(singerList.getTotal(), dtoList);
    }

    @Override
    public boolean batchUpdateSingerStatus(int appId, List<SingerInfoDTO> singerInfoDTOList, SingerStatusEnum currentSingerStatus, SingerStatusEnum targetSingerStatus, String operator) {
        if (CollUtil.isEmpty(singerInfoDTOList)) {
            return true;
        }

        //获取出认证记录Id
        List<Long> verifyId = singerInfoDTOList.stream().map(SingerInfoDTO::getSingerVerifyId).collect(Collectors.toList());
        List<SingerVerifyRecord> verifyRecordList = singerVerifyApplyDao.getSingerVerifyRecordByIds(verifyId);
        if (CollUtil.isEmpty(verifyRecordList)) {
            return true;
        }
        Map<Long, SingerVerifyRecord> verifyRecordMap = verifyRecordList.stream().collect(Collectors.toMap(SingerVerifyRecord::getId, Function.identity()));
        Map<Long, String> applySongInfoMap = singerVerifyApplySongInfoDao.getApplySongInfoMap(verifyId);
        for (SingerInfoDTO singerInfoDTO : singerInfoDTOList) {
            SingerVerifyRecord singerVerifyRecord = verifyRecordMap.get(singerInfoDTO.getSingerVerifyId());
            if (singerVerifyRecord == null) {
                log.error("singerVerifyRecord is null. singerVerifyId:{}", singerInfoDTO.getSingerVerifyId());
                continue;
            }
            SingerInfo singerInfo = SingerInfoConvert.I.buildSingerInfo(singerVerifyRecord, singerInfoDTO, targetSingerStatus.getStatus(), singerVerifyRecord.getSingerType(), operator);
            singerInfo.setSongStyle(StringUtils.isEmpty(singerVerifyRecord.getSongStyle()) ? applySongInfoMap.get(singerInfoDTO.getSingerVerifyId()) : singerVerifyRecord.getSongStyle());
            boolean res = singerInfoDao.updateSingerStatusOrInsert(currentSingerStatus.getStatus(), singerInfo);
            log.info("updateSingerStatusOrInsert res:{}", res);
        }
        return true;
    }

    @Override
    public void syncSingerSign(int appId) {
        int pageNo = 1;
        int pageSize = 50;
        int totalPage;
        do {
            //查询出所有歌手
            PageList<SingerInfo> singerInfoList = singerInfoDao.pageSingerInfoByAppId(appId, pageNo, pageSize);
            if (CollUtil.isEmpty(singerInfoList)) {
                break;
            }
            totalPage = singerInfoList.getTotal() / pageSize;
            for (SingerInfo singerInfo : singerInfoList) {
                try {
                    UserInFamilyBean userInFamily = familyManager.getUserInFamily(singerInfo.getUserId());
                    long njId = 0;
                    long familyId = 0;
                    if (userInFamily != null && userInFamily.getFamilyId() != null && userInFamily.getNjId() != null) {
                        njId = userInFamily.getNjId();
                        familyId = userInFamily.getFamilyId();
                    }
                    //判断是否需要修改签约信息
                    if (singerInfo.getNjId() == njId && singerInfo.getFamilyId() == familyId) {
                        //签约信息相同就不用更新了
                        //睡一把
                        Thread.sleep(20);
                        continue;
                    }
                    boolean res = singerVerifyApplyManager.updateSignInfo(appId, singerInfo.getUserId(), familyId, njId);
                    log.info("updateSignInfo,appId:{}, singerId:{}, njId:{}, familyId:{},res:{}", appId, singerInfo.getUserId(), njId, familyId, res);
                } catch (InterruptedException e) {
                    log.error("updateSingerSignInfo error, userId={}", singerInfo.getUserId(), e);
                }
            }
            pageNo++;
        } while (pageNo <= totalPage);
    }

}
