package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerWhiteListConfigMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class SingerWhiteListConfigDao {

    @Resource
    private SingerWhiteListConfigMapper singerWhiteListConfigMapper;

    /**
     * 根据appId和userId查询白名单配置
     *
     * @param appId  应用ID
     * @param userId 用户ID（对应歌手ID）
     * @return 白名单配置列表
     */
    public List<SingerWhiteListConfig> getSingerWhiteListConfig(Integer appId, Long userId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andSingerIdEqualTo(userId)
                .andDeployEnvEqualTo(deployEnv);
        return singerWhiteListConfigMapper.selectByExample(example);
    }

    /**
     * 批量删除白名单用户
     *
     * @param whiteListConfigs 白名单列表
     * @return 结果
     */
    public boolean batchDeleteWhiteList(List<SingerWhiteListConfig> whiteListConfigs) {
        if (CollectionUtils.isEmpty(whiteListConfigs)) {
            return true;
        }
        for (SingerWhiteListConfig whiteListConfig : whiteListConfigs) {
            SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
            example.createCriteria()
                    .andAppIdEqualTo(whiteListConfig.getAppId())
                    .andSingerIdEqualTo(whiteListConfig.getSingerId())
                    .andSingerTypeEqualTo(whiteListConfig.getSingerType())
                    .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
            long row = singerWhiteListConfigMapper.deleteByExample(example);
            if (row <= 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据歌手列表批量查询白名单列表
     *
     * @param singerInfoList 歌手列表
     * @return 白名单列表
     */
    public List<SingerWhiteListConfig> batchGetSingerWhiteList(List<SingerInfoDTO> singerInfoList) {
        List<Long> singerIdList = singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(singerIdList)) {
            return Collections.emptyList();
        }
        //根据歌手ID列表批量查询出白名单列表
        SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
        example.createCriteria()
                .andSingerIdIn(singerIdList)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<SingerWhiteListConfig> res = singerWhiteListConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyList();
        }
        //过滤出待singerInfoList中歌手类型和歌手ID都相同的白名单用户
        return res.stream()
                .filter(item -> singerInfoList.stream()
                        .anyMatch(singerInfo -> Objects.equals(singerInfo.getSingerType(), item.getSingerType()) && Objects.equals(singerInfo.getUserId(), item.getSingerId())))
                .collect(Collectors.toList());
    }
}
