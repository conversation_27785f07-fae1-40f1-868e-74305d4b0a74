package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IRoomMarkGiveProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static fm.lizhi.ocean.wavecenter.service.activitycenter.handler.DressUpGiveHandler.GIVE_DRESS_UP;

@Slf4j
@Component
public class XmRoomMarkGiveProcess implements IRoomMarkGiveProcess {

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityMaterielManager activityMaterielManager;


    @Override
    public Result<Void> giveDressUp(DressUpGiveContext context) {
        try {
            // xm 的角标不是装扮, 需要反查 URL
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
            ActivityInfoDTO activityInfo = activityApplyManager.getActivityApplyInfoById(resourceGiveDTO.getActivityId());

            //  接口进行设置角标
            Result<Void> result = activityMaterielManager.editRoomMark(context, activityInfo.getRoomMarkUrl());
            if (RpcResult.isFail(result)){
                log.warn("XmRoomMarkGiveProcess.giveDressUp fail. activityId={}, userId={}, roomMarkUrl={}",
                        resourceGiveDTO.getActivityId(), context.getDressUpGiveDTO().getUserId(), activityInfo.getRoomMarkUrl());
                return RpcResult.fail(GIVE_DRESS_UP, ResourceGiveErrorTipConstant.ROOM_MARK_GIVE_FAIL);
            }
            return RpcResult.success();
        } catch (Exception e) {
            log.error("XmRoomMarkGiveProcess.giveDressUp error", e);
            return RpcResult.fail(GIVE_DRESS_UP, ResourceGiveErrorTipConstant.ROOM_MARK_GIVE_FAIL);
        }
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}