package fm.lizhi.ocean.wavecenter.service.activitycenter.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/10/27 10:19
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-activity-center")
public class ActivityConfig extends AbsBizConfig<CommonActivityConfig> {

    /**
     * 最大活动名称长度
     */
    private Integer maxActivityNameLength = 20;

    /**
     * 活动目标，不超过100字
     */
    private Integer maxActivityGoalLength = 100;

//    /**
//     * 活动介绍，不超过100字
//     */
//    private Integer maxActivityIntroductionLength = 100;

    /**
     * 房间公告，不超过500字
     */
    private Integer maxRoomAnnouncementLength = 150;

    /**
     * 活动辅助道具图片地址
     */
    private Integer maxAuxiliaryPropUrlCount = 3;

    /**
     * 最大陪档主播数量
     */
    private Integer maxAccompanyNjIdsCount = 15;

    /**
     * 房间公告图片，不超过3个
     */
    private Integer maxRoomAnnouncementImgUrlCount = 3;

    /**
     * 距离活动开始最少剩余多少分钟可执行设置操作
     */
    private Integer minPreactDeployAnnouncement = 50000;


    /**
     * 活动结束后超过多少分钟的不做操作了
     */
    private Integer maxPreactRecoveryAnnouncement = 50000;

    /**
     * 距离活动开始最少剩余多少分钟可执行发放资源操作
     */
    private Integer minPreactGiveResource = 5;

    /**
     * 最大重新发放次数
     */
    private Integer maxTryGiveCount = 2;

    /**
     * banner图顺序
     */
    private Integer bannerSeq = 99;

    /**
     * 是否开启活动数据审核
     */
    private Boolean enableAuditActivityData = true;

    /**
     * 最大流程说明长度
     */
    private Integer maxProcessExplanationLength = 500;

    /**
     * 活动申请拒绝文案
     */
    private String applyRejectText = "【提报活动通知】\n直播间：%s（%s）\n活动时间：%s~%s\n活动名称：%s\n---\n未能通过审核，详情原因请联系官方内容小编调整优化。\n点击查看>>";

    /**
     * 活动申请同意文案
     */
    private String applyAgreeText = "【提报活动通知】\n直播间：%s（%s）\n活动时间：%s~%s\n活动名称：%s\n---\n提报活动已通过审批！\n资源位已完成配置，请完善活动安排，务必如期开展活动，请勿在活动期间过度闲聊或做不相干的事！\n如有任何突发事情无法如期开展活动，请务必报备官方内容小编！\n点击查看>>";

    /**
     * 最大官频位查询时间范围，天数
     */
    private int maxQueryOfficialSeatTimeDayRange = 7;

    /**
     * 实时发放资源code，多个用逗号隔开
     */
    private String realTimeGiveResourceCodes = "programme,hall_programme,official_seat,banner";

    private Integer maxPageSize = 100;

    /**
     * 活动审批提前时间量，单位分钟
     */
    private Integer preactTimeMin = 10;

    /**
     * 多少分钟之后可以审批
     */
    private Integer minuteAfterAudit = 1;

    /**
     * 转存失败最大重试次数
     */
    private Integer maxTransferTryCount = 2;

    /**
     * 活动取消提前时间量，单位分钟
     */
    private Integer preactCancelActivityTimeMin = 5;

    /**
     * 取消活动文案
     */
    private String cancelActivityText = "【提报活动通知】\n直播间：%s（%s）\n活动时间：%s~%s\n活动名称：%s\n---\n已被用户取消（昵称 %s），详情原因请前往创作中心查看。\n点击查看>>";

    /**
     * 管理员取消活动文案
     */
    private String adminCancelActivityText = "【提报活动通知】\n直播间：%s（%s）\n活动时间：%s~%s\n活动名称：%s\n---\n已经被官方运营取消，详情原因请前往创作中心查看。\n点击查看>>";

    /**
     * 活动开始前通知文案
     */
    private String activityStartNoticeText = "【提报活动通知】<br>直播间：%s（%s）<br>活动时间：%s~%s<br>活动名称：%s<br>----<br>即将在1小时后开始，请提前准备。<br>请前往 \"创作中心-活动记录\"，提前了解节目详情及流程。<br>点击查看>>";

    /**
     * 活动结束后通知文案
     */
    private String activityEndNoticeText = "【提报活动通知】<br>直播间：%s（%s）<br>活动时间：%s~%s<br>活动名称：%s<br>---<br>提报活动数据报告已生成！<br>请前往\"创作中心-活动记录\"，了解活动收入、流量趋势、送礼名单等数据。<br>点击查看>>";

    /**
     * 距离活动开始最少剩余多少分钟可执行修改活动操作
     */
    private Integer preactModifyActivityTimeMin = 10;

    /**
     * 通知活动开始时间距离当前时间剩余小时配置，默认1hour
     */
    private Integer noticeStartActivityPreactHour = 1;

    /**
     * 活动日历页面相对路径
     */
    private String activityCalendarUrl = "/app/activity/calendar";

    /**
     * web站活动数据链接
     */
    private String activityDataCalendarUrl = "/app/activity/calendar";

    /**
     * 活动分类映射开关
     */
    private Boolean enableActivityCategoryMapping = true;

    /**
     * 同步分类开关
     */
    private Boolean enableSyncCategory = false;

    /**
     * 获取近30分钟的活动进行通知
     */
    private Integer activityNotifyGapTimeMin = 30;


    private PpActivityConfig pp;

    private XmActivityConfig xm;

    private HyActivityConfig hy;

    public ActivityConfig() {
        PpActivityConfig ppConfig = new PpActivityConfig();
        XmActivityConfig xmConfig = new XmActivityConfig();
        HyActivityConfig hyConfig = new HyActivityConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }
}
