package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.FlowResourceContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GiveFlowResourceResDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.AddUserGroupUserParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 挂件发放处理器
 */
@Slf4j
@Component
public class PendantGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private UserGroupManager userGroupManager;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        Long activityId = context.getResourceGiveDTO().getActivityId();
        Long giveId = context.getFlowResourceGiveDTO().getGiveId();
        Long pendantId = context.getResourceGiveDTO().getResourceId();
        Long whiteNjUserGroupId = context.getTemplateInfoBean().getWhiteNjUserGroupId();
        Long njId = context.getFlowResourceGiveDTO().getUserId();
        try {
            AddUserGroupUserParamDTO addUserParam = new AddUserGroupUserParamDTO();
            addUserParam.setGroupId(whiteNjUserGroupId);
            addUserParam.setUserId(njId);
            Result<Void> addUserResult = userGroupManager.addUserGroupUser(addUserParam);
            if (RpcResult.isSuccess(addUserResult)) {
                log.info("PendantGiveHandler.giveFlowResource success, activityId={}, giveId={}, pendantId={}, whiteNjUserGroupId={}, njId={}",
                        activityId, giveId, pendantId, whiteNjUserGroupId, njId);
            } else {
                log.error("PendantGiveHandler.giveFlowResource fail, rCode={}, message={}, activityId={}, giveId={}, pendantId={}, whiteNjUserGroupId={}, njId={}",
                        addUserResult.rCode(), addUserResult.getMessage(), activityId, giveId, pendantId, whiteNjUserGroupId, njId);
            }
            // 将挂件ID返回, 作为业务记录ID
            GiveFlowResourceResDTO giveFlowResourceResDTO = new GiveFlowResourceResDTO();
            giveFlowResourceResDTO.setBizRecordId(pendantId);
            return RpcResult.success(giveFlowResourceResDTO);
        } catch (Exception e) {
            log.error("PendantGiveHandler.giveFlowResource error, activityId={}, giveId={}, pendantId={}, whiteNjUserGroupId={}, njId={}",
                    activityId, giveId, pendantId, whiteNjUserGroupId, njId, e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.PANDANT_GIVE_FAIL);
        }
    }

    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
        // 挂件不是实时发放的, 不需要取消发放, 直接返回成功
        return RpcResult.success();
    }

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.PENDANT.getResourceCode();
    }
}
