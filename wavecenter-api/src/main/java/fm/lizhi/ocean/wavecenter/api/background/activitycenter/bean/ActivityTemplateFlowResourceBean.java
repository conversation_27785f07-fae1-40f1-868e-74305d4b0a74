package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动模板流量资源
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateFlowResourceBean {

    /**
     * 资源配置id
     */
    @NotNull(message = "资源配置id不能为空")
    private Long resourceConfigId;

    private String resourceCode;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 流量资源扩展字段
     */
    @NotNull(message = "资源扩展字段不能为空")
    private ActivityTemplateFlowResourceExtraBean extra;

    /**
     * 资源物料图片列表
     */
    @Valid
    private List<ActivityTemplateFlowResourceImageBean> images;
}
