package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import com.alibaba.dubbo.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AutoConfigResourceEnum{

    /**
     * banner
     */
    BANNER(ActivityResourceDeployTypeConstants.AUTO_CONFIG,"banner", "首页横幅", true),

    /**
     * 官频位
     */
    OFFICIAL_SEAT(ActivityResourceDeployTypeConstants.AUTO_CONFIG,"official_seat", "官频位", true),

    /**
     * 推荐卡
     */
    REC_CARD(ActivityResourceDeployTypeConstants.AUTO_CONFIG,"rec_card", "推荐卡", false),

    /**
     * 节目单
     */
    PROGRAMME(ActivityResourceDeployTypeConstants.AUTO_CONFIG,"programme", "节目单", true),

    /**
     * 厅战节目单，仅西米
     */
    HALL_PROGRAMME(ActivityResourceDeployTypeConstants.AUTO_CONFIG,"hall_programme", "节目单", true),

    /**
     * 房间挂件
     */
    PENDANT(ActivityResourceDeployTypeConstants.AUTO_CONFIG, "pendant", "房间挂件", false),

    ;

    /**
     * 1：自动配置，2：手动配置
     * @see ActivityResourceDeployTypeConstants
     */
    private int deployType;

    private String resourceCode;

    private String resourceName;
    
    /**
      * 是否支持上传图片
      */
    private boolean supportUpload;


    /**
     * 根据资源编码获取枚举
     * @param resourceCode 资源编码
     * @return 枚举
     */
    public static AutoConfigResourceEnum getByResourceCode(String resourceCode) {

        if (StringUtils.isEmpty(resourceCode)) {
            return null;
        }

        for (AutoConfigResourceEnum autoConfigResourceEnum : AutoConfigResourceEnum.values()) {
            if (autoConfigResourceEnum.getResourceCode().equals(resourceCode)) {
                return autoConfigResourceEnum;
            }
        }
        return null;
    }
}